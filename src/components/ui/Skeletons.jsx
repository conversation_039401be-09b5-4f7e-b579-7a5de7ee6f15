export const PreQualifyExplainerSkeleton = () => {
  return (
    <div className="w-full p-6">
      <div className="mb-6 sm:mb-8">
        <div className="h-12  bg-gray-200 rounded animate-pulse mb-3 sm:mb-4 w-3/4"></div>
      </div>

      <div className="mb-10">
        <div className="h-10 sm:h-8 bg-gray-200 rounded animate-pulse mb-3 sm:mb-4 w-1/2"></div>
        <ul className="space-y-3 sm:space-y-4">
          {[1, 2, 3].map((item) => (
            <li key={item} className="flex items-start">
              <div className="mr-2 mt-0.5">
                <div className="h-6 w-6 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
              <div className="flex-1">
                <div className="h-4 sm:h-6 bg-gray-200 rounded animate-pulse mb-2 w-3/4"></div>
                <div className="h-3 sm:h-4 bg-gray-200 rounded animate-pulse w-full"></div>
              </div>
            </li>
          ))}
        </ul>
      </div>
      <div className="bg-gray-200 h-27 p-2 rounded-sm shadow-sm w-fit animate-pulse">
        <div className="w-44 h-20 bg-gray-300 rounded"></div>
      </div>
    </div>
  );
};

export const QuickLinksSectionSkeleton = () => {
  return (
    <div className="mt-12">
      <div className="h-10 bg-gray-200 rounded animate-pulse mb-6"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {[1, 2, 3].map((item) => (
          <div key={item} className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm animate-pulse">
            <div className="h-10 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="h-16 bg-gray-200 rounded animate-pulse mb-4"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const PrequalifyFAQSkeleton = () => {
  return (
    <div className="mt-16 mb-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <div className="h-8 sm:h-10 bg-gray-200 rounded animate-pulse mb-4 w-1/2 mx-auto"></div>
        </div>

        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-sm p-4 sm:p-6 divide-y divide-gray-200">
          {[1, 2, 3, 4, 5, 6, 7].map((item) => (
            <div key={item} className="py-4">
              <div className="flex w-full justify-between items-center">
                <div className="h-5 sm:h-6 bg-gray-200 rounded animate-pulse w-3/4"></div>
                <div className="h-5 w-5 bg-gray-200 rounded animate-pulse ml-6"></div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8">
          <div className="h-4 sm:h-5 bg-gray-200 rounded animate-pulse w-1/2 mx-auto"></div>
        </div>
      </div>
    </div>
  );
};

export const FundingStepsSkeleton = () => {
  return (
    <div className="mt-16 mb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-gray-50 rounded-lg overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Left side - Content skeleton */}
            <div className="lg:w-1/2 p-8 lg:p-12">
              <div className="h-8 sm:h-10 bg-gray-200 rounded animate-pulse mb-8 w-1/2"></div>

              <div className="space-y-6">
                {[1, 2, 3, 4, 5].map((item) => (
                  <div key={item} className="flex flex-col">
                    <div className="h-5 bg-gray-200 rounded animate-pulse mb-2 w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4 mt-1"></div>
                  </div>
                ))}
              </div>

              <div className="mt-8 h-12 bg-gray-200 rounded-full animate-pulse w-32"></div>
            </div>

            {/* Right side - Image skeleton */}
            <div className="lg:w-1/2 relative min-h-[400px] lg:min-h-[500px]">
              <div className="absolute inset-0 bg-gray-200 animate-pulse lg:rounded-r-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const TrustpilotReviewsSkeleton = () => {
  return (
    <div className="mt-16 mb-12">
      <div className="h-10 bg-gray-200 rounded animate-pulse mb-10  mx-auto"></div>
      <div className="flex justify-center">
        <div className="bg-gray-50 p-8 rounded-sm shadow-sm animate-pulse w-full max-w-6xl">
          <div className="flex flex-col md:flex-row justify-center gap-5 flex-wrap overflow-hidden ">
            {[1, 2, 3, 4, 5].map((item) => (
              <div
                key={item}
                className={`bg-white p-4 rounded-sm shadow-sm flex-auto md:max-w-1/5 flex-shrink-0 ${
                  item == 1 ? "" : "hidden md:block"
                }`}
              >
                <div className="flex mb-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <div key={star} className="w-4 h-4 bg-gray-300 rounded-sm mr-1 animate-pulse"></div>
                  ))}
                </div>
                <div className="h-4 bg-gray-300 rounded mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-300 rounded mb-1 animate-pulse"></div>
                <div className="h-3 bg-gray-300 rounded mb-1 animate-pulse"></div>
                <div className="h-3 bg-gray-300 rounded  animate-pulse"></div>
                <div className="h-3 bg-gray-300 rounded mt-2  animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export const FooterSkeleton = () => {
  return (
    <footer className="bg-[#23448F] text-[#B4C2F8] py-4 h-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info Skeleton */}
          <div className="lg:col-span-1">
            <div className="h-6 w-50 bg-[#3b5998] rounded mb-4 animate-pulse"></div>
            <div className="h-4 w-58 bg-[#3b5998] rounded mb-2 animate-pulse"></div>
            <div className="h-4 w-48 bg-[#3b5998] rounded mb-6 animate-pulse"></div>
            <div className="flex gap-3 sm:gap-4">
              <div className="h-10 w-20 bg-[#3b5998] rounded animate-pulse"></div>
              <div className="h-10 w-20 bg-[#3b5998] rounded animate-pulse"></div>
            </div>
          </div>
          {/* Loan Options Skeleton */}
          <div>
            <div className="h-5 w-24 bg-[#3b5998] rounded mb-4 animate-pulse"></div>
            <div className="space-y-2">
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
            </div>
          </div>
          {/* Resources Skeleton */}
          <div>
            <div className="h-5 w-24 bg-[#3b5998] rounded mb-4 animate-pulse"></div>
            <div className="space-y-2">
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse"></div>
            </div>
          </div>
          {/* Follow Us Skeleton */}
          <div>
            <div className="h-5 w-24 bg-[#3b5998] rounded mb-4 animate-pulse"></div>
            <div className="flex space-x-4 mb-6">
              <div className="h-6 w-6 bg-[#3b5998] rounded-full animate-pulse"></div>
              <div className="h-6 w-6 bg-[#3b5998] rounded-full animate-pulse"></div>
              <div className="h-6 w-6 bg-[#3b5998] rounded-full animate-pulse"></div>
            </div>
            <div className="h-4 w-32 bg-[#3b5998] rounded animate-pulse mb-1"></div>
            <div className="h-4 w-24 bg-[#3b5998] rounded animate-pulse"></div>
          </div>
        </div>
      </div>
      <hr className="w-full h-px max-w-7xl mt-3 sm:mt-4 mb-0 mx-auto bg-[#3b5998] border-0 animate-pulse" />
      <div className="py-3 sm:py-4">
        <div className="h-4 w-64 bg-[#3b5998] rounded mx-auto animate-pulse"></div>
      </div>
    </footer>
  );
};
