import { useEffect, useRef } from "react";
import { loadTrustpilotScript } from "../../utils/trustpilot";

const TrustPilotWidget = () => {
  const widgetDivRef = useRef(null);

  useEffect(() => {
    let cancelled = false;
    loadTrustpilotScript().then(() => {
      if (!cancelled && widgetDivRef.current && window.Trustpilot) {
        window.Trustpilot.loadFromElement(widgetDivRef.current);
      }
    });
    return () => {
      cancelled = true;
    };
  }, []);

  return (
    <div className="bg-gray-50 h-27 p-2 rounded-sm shadow-sm w-fit">
      <div
        ref={widgetDivRef}
        className="trustpilot-widget"
        data-locale="en-US"
        data-template-id="53aa8807dec7e10d38f59f32"
        data-businessunit-id="66d0b7dd321310cee73cdf4b"
        data-style-height=""
        data-style-width="180px"
      >
        <a href="https://www.trustpilot.com/review/pinnacleconsultingny.com" target="_blank" rel="noopener">
          <div className="bg-gray-200 h-24 p-2 rounded-sm shadow-sm w-fit animate-pulse">
            <div className="w-44 h-20 bg-gray-300 rounded"></div>
          </div>
        </a>
      </div>
    </div>
  );
};

export const PreQualifyExplainer = ({ title = "Ready to Grow Your Business?" }) => {
  return (
    <div className="w-full p-6">
      <div className="mb-6 sm:mb-8">
        <h2 className="text-xl sm:text-2xl font-bold text-blue-900 mb-3 sm:mb-4">{title}</h2>
      </div>

      <div className="mb-10">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4">Our promise to you</h3>
        <ul className="space-y-3 sm:space-y-4">
          <li className="flex items-start">
            <div className="mr-2 text-green-600 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800 text-sm sm:text-base">We Guide, You Grow</p>
              <p className="text-xs sm:text-sm text-gray-600">
                Your dedicated funding specialist will be with you every step of the way
              </p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="mr-2 text-green-600 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800 text-sm sm:text-base">No Credit Impact</p>
              <p className="text-xs sm:text-sm text-gray-600">Apply with no hard credit pull</p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="mr-2 text-green-600 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800 text-sm sm:text-base">Your Needs, Your Terms</p>
              <p className="text-xs sm:text-sm text-gray-600" data-hj-allow>
                Your business is unique, your funding should be too.
              </p>
            </div>
          </li>
        </ul>
      </div>
      <TrustPilotWidget />
    </div>
  );
};

export default PreQualifyExplainer;
