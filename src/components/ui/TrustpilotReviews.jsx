import React, { useEffect } from "react";
import { loadTrustpilotScript } from "../../utils/trustpilot";

const TrustpilotReviews = ({ title = "Trusted by Small Businesses" }) => {
  const widgetRef = React.useRef(null);
  useEffect(() => {
    let cancelled = false;
    loadTrustpilotScript().then(() => {
      if (!cancelled && widgetRef.current && window.Trustpilot) {
        window.Trustpilot.loadFromElement(widgetRef.current);
      }
    });
    return () => {
      cancelled = true;
    };
  }, []);

  return (
    <div className="mt-30 mb-20">
      <h2 className="text-3xl sm:text-4xl font-bold text-blue-900 mb-10 text-center">{title}</h2>
      <div>
        <div
          ref={widgetRef}
          className="trustpilot-widget"
          data-locale="en-US"
          data-template-id="54ad5defc6454f065c28af8b"
          data-businessunit-id="66d0b7dd321310cee73cdf4b"
          data-style-height="240px"
          data-style-width="100%"
          data-stars="1,2,3,4,5"
          data-theme="light"
          data-review-languages="en"
        >
          {/* Fallback content while loading */}
          <div className="bg-gray-50 p-8 rounded-sm shadow-sm animate-pulse">
            <div className="flex flex-col md:flex-row justify-center gap-5 flex-wrap overflow-hidden ">
              {[1, 2, 3, 4, 5].map((item) => (
                <div
                  key={item}
                  className={`bg-white p-4 rounded-sm shadow-sm flex-auto md:max-w-1/5 flex-shrink-0 ${
                    item == 1 ? "" : "hidden md:block"
                  }`}
                >
                  <div className="flex mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <div key={star} className="w-4 h-4 bg-gray-300 rounded-sm mr-1 animate-pulse"></div>
                    ))}
                  </div>
                  <div className="h-4 bg-gray-300 rounded mb-2 animate-pulse"></div>
                  <div className="h-3 bg-gray-300 rounded mb-1 animate-pulse"></div>
                  <div className="h-3 bg-gray-300 rounded mb-1 animate-pulse"></div>
                  <div className="h-3 bg-gray-300 rounded  animate-pulse"></div>
                  <div className="h-3 bg-gray-300 rounded mt-2  animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrustpilotReviews;
