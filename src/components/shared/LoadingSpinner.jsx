import { useEffect } from "react";

/**
 * Reusable loading spinner component
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the spinner (sm, md, lg)
 * @param {string} props.color - Color of the spinner (blue, gray, white)
 * @returns {JSX.Element}
 */
export const LoadingSpinner = ({ size = "md", color = "blue" }) => {
  // Size classes
  const sizeClasses = {
    sm: "w-5 h-5",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  // Color classes
  const colorClasses = {
    blue: "text-blue-500",
    gray: "text-gray-500",
    white: "text-white",
  };

  return (
    <div
      id="app-loader"
      className={`inline-block animate-spin rounded-full border-4 border-solid border-current border-r-transparent ${sizeClasses[size]} ${colorClasses[color]}`}
      role="status"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
};

/**
 * Loading overlay component that covers the entire parent element
 * @param {Object} props - Component props
 * @param {boolean} props.isLoading - Whether the overlay is visible
 * @param {string} props.message - Optional message to display
 * @returns {JSX.Element|null}
 */
export const LoadingOverlay = ({ isLoading, message }) => {
  useEffect(() => {
    if (isLoading) {
      const loader = document.getElementById("app-loader");
      if (loader) {
        loader.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }
  }, [isLoading]);

  if (!isLoading) return null;

  return (
    <div className="absolute inset-0 bg-white/85 flex flex-col items-center justify-center z-50">
      <LoadingSpinner size="lg" color="blue" />
      {message && <p className="mt-4 text-gray-700 font-medium text-center">{message}</p>}
    </div>
  );
};
