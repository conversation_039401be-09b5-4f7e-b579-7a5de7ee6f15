import { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { useNavigate } from "react-router-dom";
import { X } from "lucide-react";
import { cookiesHandler } from "../../utils/customCookieStorage";
import { RECENT_APP } from "../../utils/consts";

const WARNING_MODAL_KEY = "duplicate-modal-closed";

export const DuplicateSubmissionWarningModal = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const buttonRef = useRef(null);
  const [modalClosed] = useState(() => sessionStorage.getItem(WARNING_MODAL_KEY) === "true");
  const [recentApp] = useState(() => cookiesHandler.get(RECENT_APP));

  useEffect(() => {
    const initialOverflow = document.body.style.overflow;
    if (isOpen && !modalClosed) {
      document.body.style.overflow = "hidden";
      buttonRef.current.focus();
    }
    return () => {
      document.body.style.overflow = initialOverflow || "auto";
    };
  }, [isOpen, modalClosed]);

  const handleViewApplication = () => {
    const app = cookiesHandler.get(RECENT_APP);
    if (app) {
      navigate(`/prequalify-result/${app.uuid}`);
    }
    onClose();
  };

  if (!isOpen || modalClosed) return null;

  return (
    <Portal>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-2 bg-black/50">
        <div className="bg-white rounded-md shadow-2xl max-w-md w-full p-4 relative">
          <div className="flex items-center justify-between mb-5">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">Application Already Submitted</h3>
            <button
              onClick={() => {
                sessionStorage.setItem(WARNING_MODAL_KEY, "true");
                onClose();
              }}
              className="text-gray-400 hover:text-gray-600 transition-colors focus:outline-none"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="mb-6 text-sm text-gray-700 leading-relaxed">
            Please review your existing application. For changes or questions, contact{" "}
            {recentApp?.agent?.name ? "your funding specialist" : "our office"}.
          </div>

          <div className="flex justify-end gap-3">
            {recentApp?.agent && (
              <button
                onClick={() => {
                  const email = recentApp.agent.email;
                  const subject = `Re: ${recentApp.preQualifyFields.businessName} - Funding Application`;
                  const body = `Hi ${recentApp.agent.name.split(" ")[0]}!\n\nI need some help with my application.\n\n\n\nBest,\n${recentApp.preQualifyFields.firstName}`;
                  const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
                  window.open(mailtoLink, "_blank");
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
              >
                Contact My Funding Specialist
              </button>
            )}
            <button
              ref={buttonRef}
              onClick={handleViewApplication}
              className="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium px-4 py-2 rounded-md transition-colors focus:outline-none"
            >
              View My Application
            </button>
          </div>
        </div>
      </div>
    </Portal>
  );
};

function Portal({ children }) {
  return createPortal(children, document.body);
}
