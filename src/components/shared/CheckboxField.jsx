import { use<PERSON><PERSON>roll<PERSON> } from "react-hook-form";

/**
 * Checkbox field component that integrates with react-hook-form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @returns {JSX.Element}
 */
export const CheckboxField = ({
  name,
  label,
  control,
  rules = {},
  defaultChecked,

  ...rest
}) => {
  const {
    field: { value, onChange, ref },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: defaultChecked || true,
  });

  return (
    <div className="mb-6">
      <div className="flex items-center">
        <input
          type="checkbox"
          checked={value}
          onChange={(e) => onChange(e.target.checked)}
          id={name}
          name={name}
          ref={ref}
          className={`
            mr-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500
            ${error ? "border-red-500" : "border-gray-300"}
          `}
          {...rest}
        />
        <label htmlFor={name} className="text-gray-700 text-base">
          {label}
        </label>
      </div>
      {error && <p className="text-red-500 text-xs italic mt-1">{error.message}</p>}
    </div>
  );
};
