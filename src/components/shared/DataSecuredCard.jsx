export default function DataSecuredCard() {
  return (
    <div
      className="mt-4 w-full rounded-sm shadow-lg bg-white p-4 border border-gray-200 flex items-center space-x-3"
      data-hj-allow
    >
      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-100">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-green-600"
        >
          <path d="M15.686 15A14.5 14.5 0 0 1 12 22a14.5 14.5 0 0 1 0-20 10 10 0 1 0 9.542 13" />
          <path d="M2 12h8.5" />
          <path d="M20 6V4a2 2 0 1 0-4 0v2" />
          <rect width="8" height="5" x="14" y="6" rx="1" />
        </svg>
      </div>
      <div className="text-base text-gray-700 font-medium">Your data is secured using 256-bit encryption.</div>
    </div>
  );
}
