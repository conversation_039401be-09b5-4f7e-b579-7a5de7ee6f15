import React from "react";
import { use<PERSON><PERSON>roll<PERSON> } from "react-hook-form";
import { formatPhoneNumber, parsePhoneNumber } from "../../utils/formatters";

/**
 * Phone field component that integrates with react-hook-form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {string} props.initialValue - Initial value for the field
 * @param {Object} props.rules - Validation rules
 * @returns {JSX.Element}
 */
export const PhoneField = ({
  name,
  label,
  control,
  initialValue,
  rules = {},
  placeholder = "(*************",
  ...rest
}) => {
  const {
    field: { onChange, value, ref },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: initialValue || "",
  });

  // Format the display value
  const displayValue = value ? formatPhoneNumber(value) : "";

  /**
   * Handles input change events
   * @param {React.ChangeEvent<HTMLInputElement>} e - Change event
   */
  const handleChange = (e) => {
    const inputValue = e.target.value;
    // Extract numeric value from formatted string
    const numericValue = parsePhoneNumber(inputValue);
    // Update the form value with the numeric value
    onChange(numericValue);
  };
  // Create a unique ID for the input
  const inputId = `${name}-phone`;

  return (
    <div className="mb-6">
      <label htmlFor={inputId} className="block text-gray-700 text-base font-normal mb-2">
        {label}
      </label>
      <input
        data-hj-suppress
        type="tel"
        id={inputId}
        name={name}
        ref={ref}
        value={displayValue}
        onChange={handleChange}
        placeholder={placeholder}
        className={`
          w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
          ${error ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"}
        `}
        {...rest}
      />
      {error && <p className="text-red-500 text-xs italic mt-1">{error.message}</p>}
    </div>
  );
};
