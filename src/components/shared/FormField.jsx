import React from "react";
import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { FormInput } from "./FormInput";

/**
 * Form field component that integrates FormInput with react-hook-form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @param {React.ComponentProps<typeof FormInput>} props.inputProps - Props to pass to FormInput
 * @returns {JSX.Element}
 */
export const FormField = ({ name, label, control, rules = {}, inputProps = {}, ...rest }) => {
  const {
    field: { onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: "",
  });

  return (
    <FormInput
      label={label}
      error={error ? error?.message : undefined}
      {...field}
      {...inputProps}
      {...rest}
      onChange={onChange}
    />
  );
};
