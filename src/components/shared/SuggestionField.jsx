import React, { useState } from "react";
import { useFormContext } from "react-hook-form";

/**
 * Form field component with suggestion functionality
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @param {string} props.suggestion - Suggestion text to display
 * @param {string} props.suggestionLabel - Custom label for the suggestion link
 * @param {function} props.onSuggestionClick - Function to call when suggestion is clicked
 * @param {React.ComponentType} props.inputComponent - Custom input component to use instead of default input
 * @returns {JSX.Element}
 */
export const SuggestionField = ({ suggestion = "", inputComponent, onSuggestionClick, ...rest }) => {
  const [used, setUsed] = useState(false);
  const { getValues } = useFormContext();
  const value = getValues(rest.name);

  const handleSuggestionClick = () => {
    setUsed(true);
    onSuggestionClick(rest.name, suggestion);
  };

  const InputComponent = inputComponent;

  return (
    <div className="mb-6">
      <InputComponent {...rest} />
      {!value && !used && suggestion && (
        <div className="-mt-4 text-sm text-gray-400 font-light">
          Use:{" "}
          <span
            className="text-emerald-600 underline cursor-pointer hover:text-emerald-800 transition-colors"
            onMouseDown={(e) => e.preventDefault()}
            onClick={handleSuggestionClick}
          >
            {suggestion}
          </span>
        </div>
      )}
    </div>
  );
};
