/**
 * @typedef {Object} Step
 * @property {string} id - Step identifier
 * @property {string} title - Step title
 */

/**
 * @typedef {Object} ProgressBarProps
 * @property {Step[]} steps - Array of step objects
 * @property {number} currentStep - Current active step index
 */

/**
 * Reusable progress bar component for multi-step forms
 * @param {ProgressBarProps} props
 * @returns {JSX.Element}
 */
export const ProgressBar = ({ steps, currentStep, ...rest }) => {
  return (
    <div className="mb-8 flex justify-center" {...rest}>
      <div className="flex justify-between max-w-md w-full">
        {steps.map((step, index) => (
          <div key={step.id} className={`flex-1 ${index < steps.length - 1 ? "relative" : "flex-none"}`}>
            {index < steps.length - 1 && (
              <div className={`absolute top-4 w-full h-0.5 ${index < currentStep ? "bg-blue-500" : "bg-gray-300"}`} />
            )}
            <div
              className={`relative w-8 h-8 rounded-full flex items-center justify-center ${
                index <= currentStep ? "bg-blue-500 text-white" : "bg-gray-300"
              }`}
            >
              {index + 1}
            </div>
            {/* <div className="text-base mt-2">{step.title}</div> */}
          </div>
        ))}
      </div>
    </div>
  );
};
