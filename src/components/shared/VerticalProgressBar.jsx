/**
 * @typedef {Object} Step
 * @property {string} id - Step identifier
 * @property {string} title - Step title
 * @property {string} description - Step description
 */

/**
 * @typedef {Object} VerticalProgressBarProps
 * @property {Step[]} steps - Array of step objects
 * @property {number} currentStep - Current active step index
 * @property {string} className - Additional CSS classes
 */

/**
 * Vertical progress bar component for multi-step forms
 * @param {VerticalProgressBarProps} props
 * @returns {JSX.Element}
 */
export const VerticalProgressBar = ({ steps, currentStep, className = "" }) => {
  return (
    <div className={`flex flex-col ${className}`}>
      {steps.map((step, index) => (
        <div key={step.id} className="relative">
          {/* Vertical line connecting steps */}
          {index < steps.length - 1 && (
            <div
              className={`absolute left-4 top-8 w-0.5 h-[calc(100%-16px)] ${
                index < currentStep ? "bg-blue-500" : "bg-gray-300"
              }`}
            />
          )}

          <div className="flex items-start mb-8">
            {/* Step indicator circle */}
            <div
              className={`shrink-0 relative z-10 flex items-center justify-center w-8 h-8 rounded-full mr-4 ${
                index <= currentStep ? "bg-blue-500 text-white" : "bg-gray-300 text-gray-600"
              }`}
              data-hj-allow
            >
              {index + 1}
            </div>

            {/* Step content */}
            <div className="flex flex-col">
              <h3 className={`text-lg font-medium ${index <= currentStep ? "text-gray-800" : "text-gray-500"}`}>
                {step.title}
              </h3>
              {step.description && <p className="text-sm text-gray-500 mt-1">{step.description}</p>}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
