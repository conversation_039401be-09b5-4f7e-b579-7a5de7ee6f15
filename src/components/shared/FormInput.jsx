import React from "react";

/**
 * @typedef {Object} FormInputProps
 * @property {string} label - The label for the input
 * @property {string} [error] - Optional error message
 * @property {string} [className] - Optional additional CSS classes
 * @property {React.InputHTMLAttributes<HTMLInputElement>} props - All other input props
 */

/**
 * Form input component with label and error handling
 * @param {FormInputProps} props
 * @returns {JSX.Element}
 */
export const FormInput = ({ label, error, className = "", name, id, ...props }) => {
  // Ensure id is set, defaulting to name if not provided
  const inputId = id || name;

  return (
    <div className="mb-6">
      <label htmlFor={inputId} className="block text-gray-700 text-base font-normal mb-2">
        {label}
      </label>
      <input
        id={inputId}
        name={name}
        className={`
          w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
          ${error ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"}
          ${className}
        `}
        {...props}
      />
      {error && <p className="text-red-500 text-xs italic mt-1">{error}</p>}
    </div>
  );
};
