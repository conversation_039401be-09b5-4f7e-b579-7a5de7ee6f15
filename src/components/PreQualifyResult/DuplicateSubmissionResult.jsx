import { Alert<PERSON>rian<PERSON>, Phone } from "lucide-react";
import { PreQualifySuccess } from "./PreQualifySuccess";

/**
 * Duplicate submission result component that shows different messages based on application status
 * @param {Object} props - Component props
 * @param {Object} props.application - Application data
 * @returns {JSX.Element}
 */
export const DuplicateSubmissionResult = ({ application }) => {
  const { status, preQualifyFields } = application;
  const mainPhone = "(*************";
  const businessName = preQualifyFields?.businessName || "your business";

  // Determine if this is a denied or approved application
  const isDenied = status === "PREQUAL_DENIED";

  if (isDenied) {
    return (
      <div className="max-w-4xl mx-auto p-3 sm:p-6">
        <div className="bg-white rounded-sm shadow-lg p-4 sm:p-6">
          <div className="text-center mb-6">
            <AlertTriangle className="h-16 w-16 text-orange-500 mx-auto mb-4" />
            <h2 className="text-3xl sm:text-2xl font-bold text-orange-600 mb-3 sm:mb-4">
              Looks Like You Already Applied
            </h2>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-sm p-4 mb-6">
            <div className="text-center">
              <p className="text-lg text-gray-800 mb-4">
                We are unable to provide more information at this time. <br />
                Please call us for further assistance.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <a
                  href={`tel:${mainPhone.replace(/\D/g, "")}`}
                  className="bg-orange-500 hover:bg-orange-700 text-white font-semibold py-3 px-6 rounded-sm focus:outline-none focus:shadow-outline flex items-center justify-center"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Call {mainPhone}
                </a>
              </div>
            </div>
          </div>

          <div className="text-center text-sm text-gray-600">
            <p>Our team is available to help you explore other funding options.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PreQualifySuccess
      heading={"Looks Like You Already Applied"}
      description={
        <>
          You already submitted an application for <span data-hj-suppress>{businessName}</span>.
        </>
      }
      buttonText={"Continue Your Application"}
      application={application}
    />
  );
};

export default DuplicateSubmissionResult;
