import React from "react";

/**
 * Skeleton loading component for the PreQualifyResult page
 * @returns {JSX.Element}
 */
export const SkeletonLoading = () => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-sm shadow-lg p-6">
        {/* Skeleton for header section */}
        <div className="text-center mb-8">
          <div className="h-10 bg-gray-200 rounded-md w-3/4 mx-auto mb-4 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-md w-2/3 mx-auto mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded-md w-1/2 mx-auto animate-pulse"></div>
        </div>

        {/* Skeleton for middle section */}
        <div className="border-t border-b border-gray-200 py-6 mb-6">
          <div className="h-8 bg-gray-200 rounded-md w-1/2 mx-auto mb-4 animate-pulse"></div>
          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <div className="w-32 h-32 rounded-full bg-gray-200 animate-pulse"></div>
            <div className="w-full md:w-64">
              <div className="h-6 bg-gray-200 rounded-md w-3/4 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded-md w-full mb-1 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded-md w-full mb-4 animate-pulse"></div>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="h-10 bg-gray-200 rounded-md w-24 animate-pulse"></div>
                <div className="h-10 bg-gray-200 rounded-md w-36 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Skeleton for call to action */}
        <div className="text-center">
          <div className="h-5 bg-gray-200 rounded-md w-2/3 mx-auto mb-4 animate-pulse"></div>
          <div className="h-12 bg-gray-200 rounded-sm w-56 mx-auto animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};
