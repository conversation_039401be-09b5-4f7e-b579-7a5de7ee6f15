import React from "react";

/**
 * Pre-qualification denied component
 * @param {Object} props - Component props
 * @param {Object} props.application - Application data
 * @returns {JSX.Element}
 */
export const PreQualifyDenied = ({ application }) => {
  const { preQualifyFields, reason } = application;

  // Determine the specific denial message based on the reason
  const getDenialMessage = () => {
    return (
      <>
        <p className="text-xl text-gray-700 mb-4">
          Unfortunately, we are unable to pre-qualify your business for funding at this time.
        </p>
        <p className="text-lg text-gray-600 mb-6">This decision is based on the information you provided.</p>
      </>
    );
    /* TODO: disabled to always show generic deny message
    switch (reason) {
      case "start_date":
        return (
          <>
            <p className="text-xl text-gray-700 mb-4">
              Unfortunately, we are unable to pre-qualify your business for funding at this time because your business
              is less than 6 months old.
            </p>
            <p className="text-lg text-gray-600 mb-6">
              We require businesses to be operational for at least 6 months before extending funding offers.
            </p>
          </>
        );
      case "revenue":
        return (
          <>
            <p className="text-xl text-gray-700 mb-4">
              Unfortunately, we are unable to pre-qualify your business for funding at this time due to insufficient
              revenue.
            </p>
            <p className="text-lg text-gray-600 mb-6">
              We require at least $10,000 in monthly revenue to ensure repayment capability.
            </p>
          </>
        );
      case "credit_score":
        return (
          <>
            <p className="text-xl text-gray-700 mb-4">
              Unfortunately, we are unable to pre-qualify your business for funding at this time due to credit score
              requirements.
            </p>
            <p className="text-lg text-gray-600 mb-6">
              We require a minimum credit score to qualify for business funding.
            </p>
          </>
        );
      case "generic":
      default:
        return (
          <>
            <p className="text-xl text-gray-700 mb-4">
              Unfortunately, we are unable to pre-qualify your business for funding at this time.
            </p>
            <p className="text-lg text-gray-600 mb-6">This decision is based on the information you provided.</p>
          </>
        );
    } */
  };

  // Determine the next steps message based on the reason
  const getNextStepsMessage = () => {
    return (
      <>
        <p className="text-gray-700">
          You may be eligible for other financial solutions or can reapply after improving your business metrics.
        </p>
        <p className="text-gray-700">
          Our team is available to discuss alternative options that might better suit your current situation.
        </p>
      </>
    );
    /* TODO: disabled to always show generic deny message
    switch (reason) {
      case "start_date":
        return (
          <>
            <p className="text-gray-700">
              We recommend applying again once your business has been operational for at least 6 months.
            </p>
          </>
        );
      case "revenue":
        return (
          <>
            <p className="text-gray-700">
              Consider reapplying once your business has established a stronger revenue history.
            </p>
            <p className="text-gray-700">
              Focus on increasing your monthly revenue and maintaining consistent cash flow before applying again.
            </p>
          </>
        );
      case "credit_score":
        return (
          <>
            <p className="text-gray-700">Consider taking steps to improve your credit score before reapplying.</p>
            <p className="text-gray-700">
              This may include paying down existing debt, resolving any delinquencies, and maintaining a good payment
              history.
            </p>
          </>
        );
      case "generic":
      default:
        return (
          <>
            <p className="text-gray-700">
              You may be eligible for other financial solutions or can reapply after improving your business metrics.
            </p>
            <p className="text-gray-700">
              Our team is available to discuss alternative options that might better suit your current situation.
            </p>
          </>
        );
    } */
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-sm shadow-lg p-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-red-600 mb-4">We're Sorry, {preQualifyFields.firstName}</h2>
          {getDenialMessage()}
        </div>

        <div className="border-t border-b border-gray-200 py-6 mb-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">What You Can Do Next</h3>
          <div className="space-y-4 text-center">{getNextStepsMessage()}</div>
        </div>
        <div className="mt-6 text-center">
          <p className="text-gray-700">
            If you feel we made this in error please call{" "}
            <a href="tel:3476948180" className="text-blue-600 hover:underline">
              (*************
            </a>{" "}
            for reconsideration.
          </p>
        </div>
      </div>
    </div>
  );
};
