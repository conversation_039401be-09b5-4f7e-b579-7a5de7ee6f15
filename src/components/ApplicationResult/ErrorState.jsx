import React from "react";
import { Link } from "react-router-dom";

/**
 * Error state component for application result
 * @param {Object} props - Component props
 * @param {string} props.message - Error message
 * @param {string} props.errorId - Error ID
 * @returns {JSX.Element}
 */
export const ErrorState = ({ message, errorId }) => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-sm shadow-lg p-6">
        <div className="text-center mb-8">
          <svg
            className="w-16 h-16 text-red-500 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Something Went Wrong</h2>
          <p className="text-lg text-gray-700 mb-2">{message}</p>
          {errorId && <p className="text-sm text-gray-500 mb-6">Error ID: {errorId}</p>}
        </div>

        <div className="text-center">
          <p className="text-gray-700 mb-6">Please try again or contact our support team for assistance.</p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/"
              className="bg-blue-500 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-sm focus:outline-none focus:shadow-outline"
            >
              Return to Home
            </Link>
            <a
              href="tel:+3476948180"
              className="bg-green-500 hover:bg-green-700 text-white font-semibold py-2 px-6 rounded-sm focus:outline-none focus:shadow-outline"
            >
              Call Support
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
