import React from "react";
import { useNavigate } from "react-router-dom";
import { trackClick } from "../../utils/analytics";
import { APP_FLOW_STATUS } from "../../utils/consts";
import { CheckCircle, Calendar, Phone, Upload } from "lucide-react";

/**
 * Application success component
 * @param {Object} props - Component props
 * @param {Object} props.application - Application data
 * @returns {JSX.Element}
 */
export const ApplicationSuccess = ({ application }) => {
  const navigate = useNavigate();
  const { applicationFields, agent } = application;
  const firstName = applicationFields?.owners?.[0]?.firstName || "Applicant";
  const businessName = applicationFields?.businessName || "your business";

  return (
    <div className="max-w-4xl mx-auto p-3 sm:p-6">
      <div className="bg-white rounded-sm shadow-lg p-4 sm:p-6">
        <div className="text-center mb-4">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-3xl sm:text-2xl font-bold text-green-600 mb-3 sm:mb-4">Application Complete!</h2>
          <p className="text-xl text-gray-700 mb-2 mx-auto">Thank you, {firstName}!</p>
          <p className="text-lg text-gray-700 mx-auto mb-1">
            Your application for <span data-hj-suppress>{businessName}</span> has been submitted successfully.
          </p>
          <p className="text-lg text-gray-600 mx-auto">
            Our team is reviewing your application and will be in touch shortly.
          </p>
        </div>

        {/* Upload Bank Statements Button */}
        {application.status === APP_FLOW_STATUS.APP_DOCS_PENDING && (
          <div className="mb-4 text-center">
            <button
              type="button"
              onClick={() => {
                trackClick("Upload Bank Statements - Application Success", {
                  uuid: application.uuid,
                });
                navigate(`/application/${application.uuid}/upload`);
              }}
              className="inline-flex items-center bg-white border-2 border-teal-600 hover:bg-teal-600 hover:text-white text-teal-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200"
            >
              <Upload size={16} className="mr-2" />
              Upload Bank Statements
            </button>
          </div>
        )}

        {agent && (
          <div className="border-t border-gray-200 py-4 mt-4">
            <p className="text-base sm:text-lg text-center text-gray-600 mb-3 max-w-md mx-auto">
              Any questions or need personalized support?
              <br />
              Contact your Funding Specialist.
            </p>
            <div className="flex flex-col md:flex-row items-center justify-center gap-4 sm:gap-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full overflow-hidden">
                <img src={agent.image} alt={agent.name} className="w-full h-full object-cover" />
              </div>
              <div className="text-sm sm:text-base text-center md:text-left">
                <h4 className="text-base sm:text-lg font-medium text-gray-800 mb-2">{agent.name}</h4>
                <p className="text-gray-600 mb-1">
                  <span className="font-semibold">Email:</span> <span data-hj-suppress>{agent.email}</span>
                </p>
                <p className="text-gray-600 mb-3 sm:mb-4">
                  <span className="font-semibold">Phone:</span> <span data-hj-suppress>{agent.phone}</span>
                </p>
                <div className="flex flex-col sm:flex-row gap-3 text-sm w-full">
                  <a
                    href={`tel:${agent.phone.replace(/\D/g, "")}`}
                    className="bg-white border-2 border-green-600 hover:bg-green-600 hover:text-white text-green-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200 w-full sm:w-auto"
                    onClick={() => {
                      trackClick("Clicked Call Now - Application Success Page", {
                        uuid: application.uuid,
                        advisor: agent.name,
                      });
                    }}
                  >
                    <Phone className="inline-block mr-2" size={16} />
                    Call Now
                  </a>
                  <a
                    href={agent.calendlyUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white border-2 border-green-600 hover:bg-green-600 hover:text-white text-green-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200 sm:w-auto"
                    onClick={() => {
                      trackClick("Clicked Schedule Meeting - Application Success Page", {
                        uuid: application.uuid,
                        advisor: agent.name,
                      });
                    }}
                  >
                    <Calendar className="inline-block mr-2" size={16} />
                    Schedule Meeting
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
