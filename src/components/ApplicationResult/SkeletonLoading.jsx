import React from "react";

/**
 * Skeleton loading component for application result
 * @returns {JSX.Element}
 */
export const SkeletonLoading = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 animate-pulse">
      <div className="bg-white rounded-sm shadow-lg p-6">
        <div className="text-center mb-8">
          <div className="h-10 bg-gray-200 rounded w-3/4 mx-auto mb-4"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto"></div>
        </div>

        <div className="border-t border-b border-gray-200 py-6 mb-6">
          <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <div className="w-32 h-32 rounded-full bg-gray-200"></div>
            <div className="w-full md:w-1/2">
              <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="h-10 bg-gray-200 rounded w-32"></div>
                <div className="h-10 bg-gray-200 rounded w-32"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <div className="h-6 bg-gray-200 rounded w-2/3 mx-auto mb-4"></div>
          <div className="h-12 bg-gray-200 rounded w-48 mx-auto mb-4"></div>
          <div className="h-10 bg-gray-200 rounded w-32 mx-auto"></div>
        </div>
      </div>
    </div>
  );
};
