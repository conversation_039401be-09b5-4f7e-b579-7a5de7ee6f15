import React from "react";
import { FormField } from "../../shared/FormField.jsx";
import { CurrencyField } from "../../shared/CurrencyField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { validationSchema } from "../../../utils/validationSchema";
import { Options } from "../../../utils/consts";

/**
 * Funding Information step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @returns {JSX.Element}
 */
export const FundingInformation = ({ control, title = "Let’s Figure out your Funding" }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold mb-4">{title}</h3>

      <CurrencyField
        name="fundingAmount"
        label="How much funding do you need?"
        control={control}
        rules={validationSchema.fundingAmount}
        data-hj-allow
      />

      <SelectField
        label="What will you use the funds for?"
        name="purpose"
        options={Options.purpose}
        placeholder="Select purpose..."
        control={control}
        rules={validationSchema.purpose}
        data-hj-allow
      />

      <SelectField
        label="What's your Primary Funding Goal?"
        name="topPriority"
        options={Options.priority}
        placeholder="Select primary funding goal..."
        control={control}
        rules={validationSchema.topPriority}
        data-hj-allow
      />

      <SelectField
        label="How soon are you looking for funding?"
        name="timeline"
        options={Options.timeline}
        placeholder="Select timeline..."
        control={control}
        rules={validationSchema.timeline}
        data-hj-allow
      />
    </div>
  );
};
