import { useState, useEffect } from "react";
import { useController } from "react-hook-form";
import { FormField } from "../../shared/FormField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { CurrencyField } from "../../shared/CurrencyField.jsx";
import { CheckboxField } from "../../shared/CheckboxField.jsx";
import { validationSchema } from "../../../utils/validationSchema";
import { Options } from "../../../utils/consts";

/**
 * Year options for business start date
 */
const generateYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let year = currentYear; year >= 1900; year--) {
    years.push({
      value: year.toString(),
      label: year.toString(),
    });
  }
  return years;
};

const yearOptions = generateYearOptions();

/**
 * Business Information step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @returns {JSX.Element}
 */
export const BusinessInformation = ({ control, formMethods }) => {
  const {
    field: { value: monthlyRevenueValue, onChange: setMonthlyRevenue },
  } = useController({
    name: "monthlyRevenue",
    control,
  });

  const {
    field: { value: annualRevenueValue, onChange: setAnnualRevenue },
  } = useController({
    name: "annualRevenue",
    control,
  });

  // Check if annual revenue is 120k or greater
  const annualRevenueNumber = annualRevenueValue ? parseInt(annualRevenueValue, 10) : 0;
  const shouldUpgradeRevenue = annualRevenueNumber >= 120000;

  // Auto-upgrade monthly revenue selection when annual revenue is 120k+
  useEffect(() => {
    const isLowRevenue = monthlyRevenueValue === "0-10000";
    const fieldRegistered = "annualRevenue" in formMethods.getValues();

    if (shouldUpgradeRevenue && isLowRevenue) {
      setMonthlyRevenue("10000-25000");
      setAnnualRevenue(null);
      formMethods.unregister("annualRevenue");
    } else if (!isLowRevenue) {
      if (annualRevenueValue !== null) setAnnualRevenue(null);
      if (fieldRegistered) formMethods.unregister("annualRevenue");
    }
  }, [shouldUpgradeRevenue, monthlyRevenueValue, annualRevenueValue, setMonthlyRevenue, setAnnualRevenue, formMethods]);

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold mb-4">Tell us about your business</h3>

      <FormField
        label="Business Name"
        name="businessName"
        type="text"
        placeholder="Acme LLC"
        control={control}
        rules={validationSchema.businessName}
        data-hj-suppress
      />

      <SelectField
        name="monthlyRevenue"
        label="Monthly Business Revenue"
        control={control}
        options={Options.revenue}
        placeholder="Select past 4-month average revenue..."
        rules={validationSchema.monthlyRevenue}
        data-hj-allow
      />

      {monthlyRevenueValue === "0-10000" && (
        <CurrencyField
          name="annualRevenue"
          label={
            <span>
              Confirm your estimated <b>total yearly</b> sales
            </span>
          }
          control={control}
          placeholder="$0"
          rules={validationSchema.annualRevenue}
          data-hj-allow
          showZero
        />
      )}

      <BusinessStartDateInput control={control} />
    </div>
  );
};

const BusinessStartDateInput = ({ control }) => {
  const {
    field: { value: businessStartDate = "", onChange: setBusinessStartDate },
    fieldState: { error },
  } = useController({
    name: "businessStartDate",
    control,
    rules: validationSchema.businessStartDate,
  });

  // Get business name for dynamic checkbox text
  const {
    field: { value: businessName = "" },
  } = useController({
    name: "businessName",
    control,
  });

  // Local state for month and year
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");

  // Initialize month and year from businessStartDate if it exists
  useEffect(() => {
    if (businessStartDate) {
      const parts = businessStartDate.split("-");
      if (parts.length === 3) {
        // Format is YYYY-MM-DD
        setYear(parts[0]);
        setMonth(parts[1]);
      }
    }
  }, [businessStartDate]);

  // Update businessStartDate when month or year changes
  const updateBusinessStartDate = (newMonth, newYear) => {
    if (newMonth && newYear) {
      // Format as YYYY-MM-DD with "01" as the day
      setBusinessStartDate(`${newYear}-${newMonth}-01`);
    } else if (!newMonth || !newYear) {
      setBusinessStartDate("");
    }
  };

  // Handle month change
  const handleMonthChange = (e) => {
    const newMonth = e.target.value;
    setMonth(newMonth);
    updateBusinessStartDate(newMonth, year);
  };

  // Handle year change
  const handleYearChange = (e) => {
    const newYear = e.target.value;
    setYear(newYear);
    updateBusinessStartDate(month, newYear);
  };

  // Calculate if business is 6 months or less old
  const isBusinessSixMonthsOrLess = () => {
    if (!businessStartDate) return false;

    const start = new Date(businessStartDate);
    const now = new Date();
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());

    return start > now ? "future" : start >= sixMonthsAgo;
  };

  // Generate dynamic checkbox text
  const getCheckboxText = () => {
    if (!businessStartDate) return "";

    const businessNameText = businessName?.trim() || "your business";

    const startDate = new Date(businessStartDate);
    if (isNaN(startDate)) return "";

    const now = new Date();

    // Compare using UTC to avoid timezone drift (especially from "YYYY-MM-DD" input)
    const startYear = startDate.getUTCFullYear();
    const startMonth = startDate.getUTCMonth();

    const currentYear = now.getUTCFullYear();
    const currentMonth = now.getUTCMonth();

    if (startYear === currentYear && startMonth === currentMonth) {
      return (
        <span>
          Please confirm you started {businessNameText} <b>this month</b>
        </span>
      );
    }

    const monthsDiff = (currentYear - startYear) * 12 + (currentMonth - startMonth);
    if (monthsDiff < 0) return "";

    return (
      <span>
        Please confirm you started {businessNameText}{" "}
        <b>
          {monthsDiff} month{monthsDiff === 1 ? "" : "s"}
        </b>{" "}
        ago
      </span>
    );
  };

  const businessAgeStatus = isBusinessSixMonthsOrLess();
  const showCheckbox = businessAgeStatus === true;

  return (
    <div className="mb-4">
      <span className="block text-gray-700 text-base font-base mb-2">When did you start your business?</span>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="mb-4">
          <label htmlFor="businessStartMonth" className="block text-gray-700 text-sm font-base mb-2">
            Month
          </label>
          <select
            id="businessStartMonth"
            name="businessStartMonth"
            value={month}
            onChange={handleMonthChange}
            data-hj-allow
            className={`
                w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
                ${error ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"}
              `}
          >
            <option value="">Select Month</option>
            {Options.months.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <div className="mb-4">
          <label htmlFor="businessStartYear" className="block text-gray-700 text-sm font-base mb-2">
            Year
          </label>
          <select
            id="businessStartYear"
            name="businessStartYear"
            value={year}
            onChange={handleYearChange}
            data-hj-allow
            className={`
                w-full px-3 py-2 border rounded-sm focus:outline-none focus:ring-2
                ${error ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"}
              `}
          >
            <option value="">Select Year</option>
            {yearOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      {error && <p className="text-red-500 text-xs italic mt-1">{error.message}</p>}

      {showCheckbox && (
        <div className="mt-4">
          <CheckboxField
            name="businessStartConfirmation"
            label={getCheckboxText()}
            control={control}
            rules={{
              required: "You must confirm your business start date to continue",
            }}
            defaultChecked={false}
            data-hj-allow
          />
        </div>
      )}
    </div>
  );
};
