import React from "react";

/**
 * Skeleton loading component for the ApplicationForm
 * Mimics the structure of the actual form to provide a smooth loading experience
 * @returns {JSX.Element}
 */
export const SkeletonLoading = () => {
  return (
    <div className="max-w-5xl mx-auto p-6">
      <div className="flex flex-col md:flex-row">
        {/* Skeleton for vertical progress bar - Outside the form card */}
        <div className="md:w-1/4 mb-6 md:mb-0 md:pr-8">
          <div className="flex flex-col">
            {/* Step 1 */}
            <div className="relative mb-8">
              <div className="absolute left-4 top-8 w-0.5 h-16 bg-gray-200 animate-pulse"></div>
              <div className="flex items-start">
                <div className="relative z-10 w-8 h-8 rounded-full mr-4 bg-gray-200 animate-pulse"></div>
                <div className="flex flex-col">
                  <div className="h-5 bg-gray-200 rounded-md w-24 animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-32 animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative mb-8">
              <div className="absolute left-4 top-8 w-0.5 h-16 bg-gray-200 animate-pulse"></div>
              <div className="flex items-start">
                <div className="relative z-10 w-8 h-8 rounded-full mr-4 bg-gray-200 animate-pulse"></div>
                <div className="flex flex-col">
                  <div className="h-5 bg-gray-200 rounded-md w-24 animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-32 animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative mb-8">
              <div className="absolute left-4 top-8 w-0.5 h-16 bg-gray-200 animate-pulse"></div>
              <div className="flex items-start">
                <div className="relative z-10 w-8 h-8 rounded-full mr-4 bg-gray-200 animate-pulse"></div>
                <div className="flex flex-col">
                  <div className="h-5 bg-gray-200 rounded-md w-24 animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-32 animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Step 4 */}
            <div className="relative">
              <div className="flex items-start">
                <div className="relative z-10 w-8 h-8 rounded-full mr-4 bg-gray-200 animate-pulse"></div>
                <div className="flex flex-col">
                  <div className="h-5 bg-gray-200 rounded-md w-24 animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-32 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="md:w-3/4">
          {/* Skeleton for form title */}
          <div className="text-center mb-6">
            <div className="h-10 bg-gray-200 rounded-md w-3/4 mx-auto animate-pulse"></div>
          </div>

          {/* Skeleton for form card */}
          <div className="bg-white rounded-sm shadow-lg p-6">
            {/* Skeleton for form fields */}
            <div className="space-y-4">
              {/* Section title */}
              <div className="h-7 bg-gray-200 rounded-md w-1/3 mb-6 animate-pulse"></div>

              {/* Form fields - using the same layout as the actual form */}
              <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse mb-4"></div>
              <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse mb-4"></div>
              <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse mb-4"></div>
              <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse mb-4"></div>

              {/* Address section */}
              <div className="mt-8 mb-4">
                <div className="h-7 bg-gray-200 rounded-md w-1/4 mb-4 animate-pulse"></div>
                <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse mb-4"></div>
                <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse mb-4"></div>

                {/* City, State, ZIP grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1">
                    <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse"></div>
                  </div>
                  <div className="md:col-span-1">
                    <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse"></div>
                  </div>
                  <div className="md:col-span-1">
                    <div className="h-20 bg-gray-200 rounded-md w-full animate-pulse"></div>
                  </div>
                </div>
              </div>

              {/* Skeleton for navigation buttons */}
              <div className="flex justify-between mt-6 pt-4 border-t border-gray-200">
                <div></div> {/* Empty div to maintain flex spacing */}
                <div className="h-10 w-32 bg-gray-200 rounded-sm animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Skeleton for Data Secured Card */}
          <div className="mt-4 h-16 bg-gray-200 rounded-sm w-full animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};
