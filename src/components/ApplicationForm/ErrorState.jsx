import React from "react";
import { Link } from "react-router-dom";

/**
 * Error state component for the ApplicationForm page
 * @param {Object} props - Component props
 * @param {string} props.message - Error message to display
 * @param {string} props.errorId - Error ID from the API
 * @returns {JSX.Element}
 */
export const ErrorState = ({ message, errorId }) => {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-sm shadow-lg p-6 text-center">
        <div className="mb-8">
          <svg
            className="w-16 h-16 text-red-500 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <div className="bg-red-50 border border-red-200 rounded-sm p-4 mb-6">
            <p className="text-gray-800 mb-2">{message || "Application not found"}</p>
            {errorId && (
              <p className="text-sm text-gray-500">
                Error ID: <span className="font-mono">{errorId}</span>
              </p>
            )}
          </div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-sm focus:outline-none focus:shadow-outline inline-block"
            >
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
