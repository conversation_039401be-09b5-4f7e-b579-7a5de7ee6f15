import React from "react";
import { useFormContext } from "react-hook-form";
import { FormField } from "../../shared/FormField.jsx";
import { PhoneField } from "../../shared/PhoneField.jsx";
import { SSNField } from "../../shared/SSNField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { PlacesFormField } from "../../shared/PlacesFormField.jsx";
import { validationSchema } from "../../../utils/validationSchema";
import { Options } from "../../../utils/consts.js";
import { trackFieldFilled, trackOwnerAddedOrRemoved } from "../../../utils/analytics.js";
import { useAppStorage } from "../../../hooks/useAppStorage.js";

const youngestDateOfBirth = new Date(new Date().setFullYear(new Date().getFullYear() - 16)).toISOString().split("T")[0];

/**
 * Owner Info step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @returns {JSX.Element}
 */
export const OwnerInfo = ({ control }) => {
  const { setApplicationForm } = useAppStorage();
  const methods = useFormContext();
  const secondOwner = methods.watch("owners[1]");

  // Handle adding a second owner
  const handleAddSecondOwner = () => {
    // Initialize second owner with default values including ownership percentage as integer

    trackOwnerAddedOrRemoved(true);

    if (!methods.getValues("owners[1]")) {
      methods.setValue("owners[1]", {
        firstName: "",
        lastName: "",
        dateOfBirth: "",
        ssn: "",
        phone: "",
        email: "",
        address: {
          line1: "",
          line2: "",
          city: "",
          state: "",
          zip: "",
        },
        ownershipPercentage: 0,
      });
    }
  };

  // Handle setting the second owner to undefined
  const removeSecondOwner = () => {
    trackOwnerAddedOrRemoved(false);
    // Set the second owner to undefined
    methods.setValue("owners[1]", undefined);

    setApplicationForm((prev) => ({
      ...prev,
      owners: [prev.owners[0]],
    }));
  };

  // Handle using business address for owner
  const handleUseBusinessAddress = () => {
    const businessAddress = methods.getValues("address");
    if (businessAddress) {
      methods.setValue("owners[0].address.line1", businessAddress.line1 || "");
      methods.setValue("owners[0].address.line2", businessAddress.line2 || "");
      methods.setValue("owners[0].address.city", businessAddress.city || "");
      methods.setValue("owners[0].address.state", businessAddress.state || "");
      methods.setValue("owners[0].address.zip", businessAddress.zip || "");

      methods.trigger([
        "owners[0].address.line1",
        "owners[0].address.city",
        "owners[0].address.state",
        "owners[0].address.zip",
      ]);

      trackFieldFilled("owners[0].address.line1", businessAddress.line1, "ApplicationForm");
      trackFieldFilled("owners[0].address.line2", businessAddress.line2, "ApplicationForm");
      trackFieldFilled("owners[0].address.city", businessAddress.city, "ApplicationForm");
      trackFieldFilled("owners[0].address.state", businessAddress.state, "ApplicationForm");
      trackFieldFilled("owners[0].address.zip", businessAddress.zip, "ApplicationForm");

      setApplicationForm((prev) => ({
        ...prev,
        owners: [
          {
            ...prev.owners[0],
            address: {
              ...prev.owners[0].address,
              line1: businessAddress.line1,
              line2: businessAddress.line2,
              city: businessAddress.city,
              state: businessAddress.state,
              zip: businessAddress.zip,
            },
          },
          ...prev.owners.slice(1),
        ],
      }));
    }
  };

  // Handle using business address for second owner
  const handleUseBusinessAddressSecondOwner = () => {
    const businessAddress = methods.getValues("address");
    if (businessAddress) {
      methods.setValue("owners[1].address.line1", businessAddress.line1 || "");
      methods.setValue("owners[1].address.line2", businessAddress.line2 || "");
      methods.setValue("owners[1].address.city", businessAddress.city || "");
      methods.setValue("owners[1].address.state", businessAddress.state || "");
      methods.setValue("owners[1].address.zip", businessAddress.zip || "");

      methods.trigger([
        "owners[1].address.line1",
        "owners[1].address.city",
        "owners[1].address.state",
        "owners[1].address.zip",
      ]);

      trackFieldFilled("owners[1].address.line1", businessAddress.line1, "ApplicationForm");
      trackFieldFilled("owners[1].address.line2", businessAddress.line2, "ApplicationForm");
      trackFieldFilled("owners[1].address.city", businessAddress.city, "ApplicationForm");
      trackFieldFilled("owners[1].address.state", businessAddress.state, "ApplicationForm");
      trackFieldFilled("owners[1].address.zip", businessAddress.zip, "ApplicationForm");

      setApplicationForm((prev) => ({
        ...prev,
        owners: [
          ...prev.owners.slice(0, 1),
          {
            ...prev.owners[1],
            address: {
              ...prev?.owners[1]?.address,
              line1: businessAddress.line1,
              line2: businessAddress.line2,
              city: businessAddress.city,
              state: businessAddress.state,
              zip: businessAddress.zip,
            },
          },
        ],
      }));
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Owner Information</h3>

      {/* First Owner Section */}
      <div className={secondOwner ? "border border-gray-300 p-4 rounded-sm mb-6" : ""}>
        {secondOwner && <h4 className="text-lg font-medium mb-4">Owner #1</h4>}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="First Name"
            name="owners[0].firstName"
            type="text"
            placeholder="John"
            control={control}
            rules={validationSchema["firstName"]}
            data-hj-suppress
          />

          <FormField
            label="Last Name"
            name="owners[0].lastName"
            type="text"
            placeholder="Smith"
            control={control}
            rules={validationSchema["lastName"]}
            data-hj-suppress
          />
        </div>

        <FormField
          label="Date of Birth"
          name="owners[0].dateOfBirth"
          type="date"
          min="1900-01-01"
          max={youngestDateOfBirth}
          control={control}
          rules={validationSchema["owners[0].dateOfBirth"]}
          data-hj-suppress
        />

        <SSNField
          name="owners[0].ssn"
          label="Social Security Number (SSN)"
          control={control}
          rules={validationSchema["owners[0].ssn"]}
        />

        <PhoneField name="owners[0].phone" label="Phone" control={control} rules={validationSchema["phone"]} />

        <FormField
          label="Email"
          name="owners[0].email"
          type="email"
          placeholder="<EMAIL>"
          control={control}
          rules={validationSchema["email"]}
          data-hj-suppress
        />

        <div className="mt-6 mb-4">
          <div className="flex justify-between items-center mb-2">
            <h5 className="text-md font-medium text-gray-800">Home Address</h5>
            <button
              type="button"
              onClick={handleUseBusinessAddress}
              className="text-blue-600 text-sm hover:text-blue-800 transition-colors"
            >
              Same as Business
            </button>
          </div>

          <PlacesFormField
            label="Street Address"
            name="owners[0].address.line1"
            placeholder="123 Main St"
            // used to disable autocomplete
            autoComplete="new-password"
            control={control}
            data-hj-suppress
            setAddressComponents={(components) => {
              const { city, state, zipCode } = components;

              methods.setValue("owners[0].address.line2", "");
              methods.setValue("owners[0].address.city", city);
              methods.setValue("owners[0].address.state", state);
              methods.setValue("owners[0].address.zip", zipCode);

              methods.trigger([
                "owners[0].address.line1",
                "owners[0].address.city",
                "owners[0].address.state",
                "owners[0].address.zip",
              ]);

              trackFieldFilled("owners[0].address.line1", components.addressLine1, "ApplicationForm");
              trackFieldFilled("owners[0].address.line2", "", "ApplicationForm");
              trackFieldFilled("owners[0].address.city", components.city, "ApplicationForm");

              trackFieldFilled("owners[0].address.state", components.state, "ApplicationForm");
              trackFieldFilled("owners[0].address.zip", components.zipCode, "ApplicationForm");

              setApplicationForm((prev) => ({
                ...prev,
                owners: [
                  {
                    ...prev.owners[0],
                    address: {
                      ...prev.owners[0].address,
                      line1: components.addressLine1,
                      line2: "",
                      city: components.city,
                      state: components.state,
                      zip: components.zipCode,
                    },
                  },
                  ...prev.owners.slice(1),
                ],
              }));
            }}
            rules={validationSchema["address.line1"]}
          />

          <FormField
            label="Address Line 2 (optional)"
            name="owners[0].address.line2"
            type="text"
            placeholder="Suite 100"
            control={control}
            rules={validationSchema["address.line2"]}
            data-hj-suppress
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-1">
              <FormField
                label="City"
                name="owners[0].address.city"
                type="text"
                placeholder="New York"
                control={control}
                rules={validationSchema["address.city"]}
                data-hj-allow
              />
            </div>
            <div className="md:col-span-1">
              <SelectField
                label="State"
                name="owners[0].address.state"
                control={control}
                rules={validationSchema["address.state"]}
                options={Options.state}
                data-hj-allow
              />
            </div>
            <div className="md:col-span-1">
              <FormField
                label="ZIP Code"
                name="owners[0].address.zip"
                type="text"
                placeholder="10001"
                maxLength="5"
                control={control}
                rules={validationSchema["address.zip"]}
                data-hj-suppress
              />
            </div>
          </div>
        </div>

        <FormField
          label="Ownership Percentage"
          name="owners[0].ownershipPercentage"
          type="number"
          min="1"
          max="100"
          placeholder="100"
          control={control}
          rules={validationSchema["owners[0].ownershipPercentage"]}
          inputProps={{
            valueasnumber: "true",
          }}
          data-hj-allow
        />
      </div>

      {/* Second Owner Section (conditionally rendered) */}
      {secondOwner && (
        <div className="border border-gray-300 p-4 rounded-sm mb-6">
          <h4 className="text-lg font-medium mb-4">Owner #2</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="First Name"
              name="owners[1].firstName"
              type="text"
              placeholder="Jane"
              control={control}
              rules={validationSchema["firstName"]}
              data-hj-suppress
            />

            <FormField
              label="Last Name"
              name="owners[1].lastName"
              type="text"
              placeholder="Doe"
              control={control}
              rules={validationSchema["lastName"]}
              data-hj-suppress
            />
          </div>

          <FormField
            label="Date of Birth"
            name="owners[1].dateOfBirth"
            type="date"
            min="1900-01-01"
            max={youngestDateOfBirth}
            control={control}
            rules={validationSchema["owners[0].dateOfBirth"]}
            data-hj-suppress
          />

          <SSNField
            name="owners[1].ssn"
            label="Social Security Number (SSN)"
            control={control}
            rules={validationSchema["owners[0].ssn"]}
          />

          <PhoneField
            name="owners[1].phone"
            label="Phone"
            control={control}
            rules={validationSchema["phone"]}
            data-hj-suppress
          />

          <FormField
            label="Email"
            name="owners[1].email"
            type="email"
            placeholder="<EMAIL>"
            control={control}
            rules={validationSchema["email"]}
            data-hj-suppress
          />

          <div className="mt-6 mb-4">
            <div className="flex justify-between items-center mb-2">
              <h5 className="text-md font-medium text-gray-800">Home Address</h5>
              <button
                type="button"
                onClick={handleUseBusinessAddressSecondOwner}
                className="text-blue-600 text-sm hover:text-blue-800 transition-colors"
              >
                Same as Business
              </button>
            </div>

            <PlacesFormField
              label="Street Address"
              name="owners[1].address.line1"
              placeholder="123 Main St"
              // used to disable autocomplete
              autoComplete="new-password"
              control={control}
              data-hj-suppress
              setAddressComponents={(components) => {
                const { addressLine1, city, state, zipCode } = components;
                methods.setValue("owners[1].address.line2", "");
                methods.setValue("owners[1].address.city", city);
                methods.setValue("owners[1].address.state", state);
                methods.setValue("owners[1].address.zip", zipCode);

                methods.trigger([
                  "owners[1].address.line1",
                  "owners[1].address.city",
                  "owners[1].address.state",
                  "owners[1].address.zip",
                ]);

                trackFieldFilled("owners[1].address.line1", addressLine1, "ApplicationForm");
                trackFieldFilled("owners[1].address.line2", "", "ApplicationForm");
                trackFieldFilled("owners[1].address.city", city, "ApplicationForm");
                trackFieldFilled("owners[1].address.state", state, "ApplicationForm");
                trackFieldFilled("owners[1].address.zip", zipCode, "ApplicationForm");

                setApplicationForm((prev) => ({
                  ...prev,
                  owners: [
                    ...prev.owners.slice(0, 1),
                    {
                      ...prev.owners[1],
                      address: {
                        ...prev.owners[1].address,
                        line1: addressLine1,
                        line2: "",
                        city: city,
                        state: state,
                        zip: zipCode,
                      },
                    },
                  ],
                }));
              }}
              rules={validationSchema["address.line1"]}
            />

            <FormField
              label="Address Line 2 (optional)"
              name="owners[1].address.line2"
              type="text"
              placeholder="Suite 100"
              control={control}
              rules={validationSchema["address.line2"]}
              data-hj-suppress
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-1">
                <FormField
                  label="City"
                  name="owners[1].address.city"
                  type="text"
                  placeholder="New York"
                  control={control}
                  rules={validationSchema["address.city"]}
                  data-hj-allow
                />
              </div>
              <div className="md:col-span-1">
                <SelectField
                  label="State"
                  name="owners[1].address.state"
                  control={control}
                  rules={validationSchema["address.state"]}
                  options={Options.state}
                  data-hj-allow
                />
              </div>
              <div className="md:col-span-1">
                <FormField
                  label="ZIP Code"
                  name="owners[1].address.zip"
                  type="text"
                  placeholder="10001"
                  maxLength="5"
                  control={control}
                  rules={validationSchema["address.zip"]}
                  data-hj-suppress
                />
              </div>
            </div>
          </div>

          <FormField
            label="Ownership Percentage"
            name="owners[1].ownershipPercentage"
            type="number"
            min="1"
            max="100"
            placeholder="50"
            control={control}
            rules={validationSchema["owners[0].ownershipPercentage"]}
            inputProps={{
              valueasnumber: "true",
            }}
            data-hj-allow
          />
        </div>
      )}

      {/* Add Second Owner Button */}
      {!secondOwner && (
        <div className="mt-4">
          <button
            type="button"
            onClick={handleAddSecondOwner}
            className="px-4 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-700 transition-colors"
          >
            Add Second Owner
          </button>
        </div>
      )}

      {/* Set Second Owner to Undefined Button */}
      {secondOwner && (
        <div className="mt-4">
          <button
            type="button"
            onClick={removeSecondOwner}
            className="px-4 py-2 bg-gray-600 text-white rounded-sm hover:bg-gray-700 transition-colors"
          >
            Remove Second Owner
          </button>
        </div>
      )}
    </div>
  );
};
