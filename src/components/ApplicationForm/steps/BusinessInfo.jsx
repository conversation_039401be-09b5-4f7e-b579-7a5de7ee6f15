import React from "react";
import { FormField } from "../../shared/FormField.jsx";
import { PhoneField } from "../../shared/PhoneField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { EINField } from "../../shared/EINField.jsx";
import { SuggestionField } from "../../shared/SuggestionField.jsx";
import { PlacesFormField } from "../../shared/PlacesFormField.jsx";
import { validationSchema } from "../../../utils/validationSchema";
import { Options } from "../../../utils/consts";
import { useFormContext } from "react-hook-form";
import { formatPhoneNumber, parsePhoneNumber } from "../../../utils/formatters.js";
import { trackFieldFilled } from "../../../utils/analytics.js";
import { useAppStorage } from "../../../hooks/useAppStorage.js";

const today = new Date().toISOString().split("T")[0];

/**
 * Business Info step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.preQualifyResult - Pre-qualification result data
 * @param {Function} props.onSuggestionClick - Function to handle suggestion clicks
 * @returns {JSX.Element}
 */
export const BusinessInfo = ({ preQualifyResult, onSuggestionClick, control }) => {
  const { setApplicationForm } = useAppStorage();
  const methods = useFormContext();
  const { phone, email, businessName } = preQualifyResult || {};

  // Determine entity type suggestion based on business name
  const getEntityTypeSuggestion = () => {
    if (!businessName) return null;

    if (businessName.toLowerCase().includes("llc")) {
      return "LLC";
    } else if (businessName.toLowerCase().includes("inc") || businessName.toLowerCase().includes("corp")) {
      return "Corporation";
    }

    return null;
  };

  const entityTypeSuggestion = getEntityTypeSuggestion();

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold mb-4">Business Information</h3>

      <FormField
        label="Business Name"
        name="businessName"
        type="text"
        placeholder="Acme LLC"
        control={control}
        rules={validationSchema.businessName}
        data-hj-suppress
      />

      <FormField
        label="DBA Name (optional)"
        name="dbaName"
        type="text"
        placeholder="Doing Business As"
        control={control}
        rules={validationSchema.dbaName}
        data-hj-suppress
      />

      <FormField
        label="Website (optional)"
        name="website"
        type="text"
        placeholder="www.example.com"
        control={control}
        rules={validationSchema.website}
        data-hj-suppress
      />

      <SuggestionField
        suggestion={entityTypeSuggestion}
        onSuggestionClick={onSuggestionClick}
        inputComponent={SelectField}
        label="Entity Type"
        name="entityType"
        control={control}
        rules={validationSchema.entityType}
        options={Options.entityType}
        data-hj-allow
      />

      <EINField
        name="ein"
        label="Employer Identification Number (EIN)"
        control={control}
        rules={validationSchema.ein}
        data-hj-suppress
      />

      <SelectField
        label="Industry"
        name="industry"
        control={control}
        rules={validationSchema.industry}
        options={Options.industry}
        data-hj-allow
      />

      <FormField
        label="Business Start Date"
        name="businessStartDate"
        type="date"
        min="1900-01-01"
        max={today}
        control={control}
        rules={validationSchema.businessStartDate}
        data-hj-suppress
      />

      <SuggestionField
        suggestion={formatPhoneNumber(phone)}
        onSuggestionClick={(name, value) => {
          onSuggestionClick(name, parsePhoneNumber(value));
        }}
        inputComponent={PhoneField}
        label="Business Phone"
        name="businessPhone"
        control={control}
        rules={validationSchema.businessPhone}
        data-hj-suppress
      />

      <SuggestionField
        suggestion={email}
        inputComponent={FormField}
        onSuggestionClick={onSuggestionClick}
        label="Business Email"
        name="businessEmail"
        type="email"
        placeholder="<EMAIL>"
        control={control}
        rules={validationSchema.businessEmail}
        data-hj-suppress
      />

      <div className="mt-8 mb-4">
        <h4 className="text-lg font-medium mb-4">Business Address</h4>

        <PlacesFormField
          label="Street Address"
          name="address.line1"
          placeholder="123 Main Street"
          // used to disable autocomplete
          autoComplete="new-password"
          control={control}
          data-hj-suppress
          setAddressComponents={(components) => {
            const { addressLine1, city, state, zipCode } = components;

            methods.setValue("address.line2", "");
            methods.setValue("address.city", city);
            methods.setValue("address.state", state);
            methods.setValue("address.zip", zipCode);

            methods.trigger(["address.line1", "address.city", "address.state", "address.zip"]);

            trackFieldFilled("address.line1", addressLine1, "ApplicationForm");
            trackFieldFilled("address.line2", "", "ApplicationForm");
            trackFieldFilled("address.city", city, "ApplicationForm");
            trackFieldFilled("address.state", state, "ApplicationForm");
            trackFieldFilled("address.zip", zipCode, "ApplicationForm");

            setApplicationForm((prev) => ({
              ...prev,
              address: {
                ...prev.address,
                line1: addressLine1,
                line2: "",
                city: city,
                state: state,
                zip: zipCode,
              },
            }));
          }}
          rules={validationSchema["address.line1"]}
        />

        <FormField
          label="Address Line 2 (optional)"
          name="address.line2"
          type="text"
          placeholder="Suite 100"
          control={control}
          rules={validationSchema["address.line2"]}
          data-hj-suppress
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1">
            <FormField
              label="City"
              name="address.city"
              type="text"
              placeholder="Brooklyn"
              control={control}
              rules={validationSchema["address.city"]}
              data-hj-allow
            />
          </div>
          <div className="md:col-span-1">
            <SelectField
              label="State"
              name="address.state"
              control={control}
              rules={validationSchema["address.state"]}
              options={Options.state}
              data-hj-allow
            />
          </div>
          <div className="md:col-span-1">
            <FormField
              label="ZIP Code"
              name="address.zip"
              type="text"
              placeholder="10001"
              maxLength="5"
              control={control}
              rules={validationSchema["address.zip"]}
              data-hj-suppress
            />
          </div>
        </div>
      </div>
    </div>
  );
};
