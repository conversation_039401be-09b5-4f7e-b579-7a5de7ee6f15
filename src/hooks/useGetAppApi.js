import { useState, useCallback, useEffect, useRef } from "react";
import { API_ENDPOINT } from "../utils/consts";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the getAppById API call
 * @param {string} appId - Application ID to fetch (optional)
 * @param {boolean} autoFetch - Whether to automatically fetch the app when the hook is initialized
 * @returns {Object} API state and methods
 */
export function useGetAppApi(appId = null, autoFetch = false, initialData = null) {
  const [status, setStatus] = useState(autoFetch && appId ? STATUS.LOADING : STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(initialData);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Fetch an application by ID
   * @param {string} id - Application ID to fetch
   * @returns {Promise<Object>} API response
   */
  const fetchApp = useCallback(async (id) => {
    let error_message = "";

    if (!id) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });

    try {
      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/${id}`);
      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        // Set the result
        setStatus(STATUS.SUCCESS);
        setResult(result.data);
      }

      // Return the response
      return result;
    } catch (error) {
      error_message = error?.error || error?.message || "Failed to load application data. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  // Track if we've already fetched for a specific appId
  const fetchedRef = useRef({});

  // Auto-fetch the app if appId is provided and autoFetch is true
  useEffect(() => {
    if (autoFetch && appId && !fetchedRef.current[appId]) {
      // Mark this appId as fetched
      fetchedRef.current[appId] = true;
      // Fetch from the API
      fetchApp(appId);
    }
  }, [appId, autoFetch, fetchApp]);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    fetchApp,
    reset,
  };
}
