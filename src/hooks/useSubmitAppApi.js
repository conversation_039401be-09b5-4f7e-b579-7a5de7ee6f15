import { useState, useCallback } from "react";
import { trackCustomEvent } from "../utils/analytics";
import { API_ENDPOINT } from "../utils/consts";
import { calculateAge, calculateMonthsInBusiness } from "../utils/dateUtils";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the submitApp API call
 * @returns {Object} API state and methods
 */
export function useSubmitAppApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Submit an application
   * @param {string} uuid - Application ID to submit
   * @param {Object} formData - Form data to submit
   * @returns {Promise<Object>} API response
   */
  const submitApp = useCallback(async (uuid, formData) => {
    let result_status = "unknown";
    let error_message = "";

    if (!uuid) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      // Prepare the request body
      const requestBody = {
        applicationFields: formData,
      };

      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/${uuid}/submit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        result_status = "success";

        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      error_message =
        error?.error || error?.message || "There was a problem submitting your application. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    } finally {
      const startedAt = Number(sessionStorage.getItem("application_form_started_at"));
      const submittedAt = Number(sessionStorage.getItem("application_form_submitted_at"));
      const duration = Math.round((submittedAt - startedAt) / 1000) || null;

      const { entityType, industry, businessStartDate } = formData;

      const eventParams = {
        entityType,
        industry,
        businessStartDate,
        city: formData.address.city,
        state: formData.address.state,
        zip: formData.address.zip,
        owners: formData.owners.length,
        owner1_age: calculateAge(formData.owners[0].dateOfBirth),
        owner1_city: formData.owners[0].address.city,
        owner1_state: formData.owners[0].address.state,
        owner1_zip: formData.owners[0].address.zip,
        owner1_percentage: formData.owners[0].ownershipPercentage,
        owner2_age: null,
        owner2_city: null,
        owner2_state: null,
        owner2_zip: null,
        owner2_percentage: null,
        months_in_business: calculateMonthsInBusiness(businessStartDate),
        form_duration: duration,
      };

      if (formData.owners[1]) {
        eventParams.owner2_age = calculateAge(formData.owners[1].dateOfBirth);
        eventParams.owner2_city = formData.owners[1].address.city;
        eventParams.owner2_state = formData.owners[1].address.state;
        eventParams.owner2_zip = formData.owners[1].address.zip;
        eventParams.owner2_percentage = formData.owners[1].ownershipPercentage;
      }

      trackCustomEvent("application_form_submitted", eventParams, false);
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    submitApp,
    reset,
  };
}
