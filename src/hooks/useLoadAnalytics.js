import { useState, useEffect } from "react";
import { logger } from "../utils/logger";
import { initializeGA } from "../utils/analytics";
import { initializeGTM } from "../utils/gtm";
import { initializeHotjar } from "../utils/hotjar";

const gaTrackingId = import.meta.env.VITE_GA_TRACKING_ID;
const adwordsId = import.meta.env.VITE_ADWORDS_ID;

export default function useLoadAnalytics() {
  const [analyticsLoaded, setAnalyticsLoaded] = useState(false);

  useEffect(() => {
    logger.log("Loading analytics");

    initializeGA(gaTrackingId, adwordsId);
    initializeGTM();
    initializeHotjar();

    setAnalyticsLoaded(true);
  }, []);

  return analyticsLoaded;
}
