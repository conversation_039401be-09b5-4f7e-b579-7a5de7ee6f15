import { useState, useCallback } from "react";
import { API_ENDPOINT } from "../utils/consts";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the PandaDoc status API call
 * This API checks if the document has been signed
 * @returns {Object} API state and methods
 */
export function usePandaDocStatusApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Check the PandaDoc document status
   * @param {string} uuid - Application ID to check status for
   * @returns {Promise<Object>} API response with signed status
   */
  const checkDocumentStatus = useCallback(async (uuid) => {
    let error_message = "";

    if (!uuid) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });

    try {
      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/${uuid}/pandadoc/status`);
      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        // Set the result
        setStatus(STATUS.SUCCESS);
        setResult(result);
      }

      // Return the response
      return result;
    } catch (error) {
      error_message = error?.error || error?.message || "Failed to check document status. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    checkDocumentStatus,
    reset,
  };
}
