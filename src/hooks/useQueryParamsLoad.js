import { useMemo, useRef } from "react";
import { useAppStorage } from "./useAppStorage";

const deserializeParams = (queryString) => {
  if (!queryString) return null;

  const qParams = new URLSearchParams(queryString);
  const params = {};

  // Convert query parameters to form data
  qParams.forEach((value, key) => {
    // Only convert "true"/"false" strings to booleans, keep everything else as strings
    if (value === "true") {
      params[key] = true;
    } else if (value === "false") {
      params[key] = false;
    } else {
      // Keep all other values as strings
      const numberFields = ["fundingAmount", "currentStep"];
      if (numberFields.includes(key)) {
        params[key] = Number(value);
      } else {
        params[key] = String(value);
      }
    }
  });

  return params;
};

export function useQueryParamsLoad() {
  const initRef = useRef(false);
  const { utmParams, preQualificationFormParams, fastTrackFormParams, applicationFormParams, otherParams } = useMemo(
    () => getCategorizedParams(),
    [],
  );
  const { utmParams: storedUtmParams, setUtmParams, setPreQualifyForm, setFastTrackForm } = useAppStorage();

  if (!initRef.current) {
    initRef.current = true;

    // Store UTM params in individual cookies
    if (utmParams) {
      const newUtmParams = { ...storedUtmParams, ...utmParams };
      setUtmParams(newUtmParams);
    }

    if (preQualificationFormParams) {
      setPreQualifyForm(preQualificationFormParams);
    }

    if (fastTrackFormParams) {
      setFastTrackForm(fastTrackFormParams);
    }

    const params = new URLSearchParams(otherParams).toString();
    const replaceUrl = `${window.location.pathname}${params ? "?" + params : ""}`;

    window.history.replaceState({}, document.title, replaceUrl);

    // if on fast-track page, keep utm_rep in url
    if (window.location.pathname === "/ft") {
      const utmRep = utmParams?.utm_rep;
      if (utmRep) {
        const url = new URL(window.location.href);
        url.searchParams.set("utm_rep", utmRep);
        window.history.replaceState({}, document.title, url);
      }
    }
  }

  return {
    utmParams,
    preQualificationFormParams,
    fastTrackFormParams,
    applicationFormParams,
    otherParams,
  };
}

const preQualificationFormKeys = [
  "fundingAmount",
  "purpose",
  "topPriority",
  "timeline",
  "businessName",
  "monthlyRevenue",
  "annualRevenue",
  "businessStartDate",
  "firstName",
  "lastName",
  "email",
  "phone",
  "estimatedFICO",
  "consent",
  "currentStep",
];

const fastTrackFormKeys = ["businessName", "firstName", "lastName", "email", "phone", "estimatedFICO", "consent"];

export function getCategorizedParams() {
  const params = deserializeParams(window.location.search);

  if (!params) return {};

  let utmParams = null;
  let preQualificationFormParams = null;
  let fastTrackFormParams = null;
  let otherParams = {};

  for (const [key, value] of Object.entries(params)) {
    const isClickId = ["gclid", "wraid", "gbraid"].includes(key.toLowerCase());
    const isUtm = key.startsWith("utm_");
    if (isUtm || isClickId) {
      if (!utmParams) utmParams = {};
      if (isClickId) {
        utmParams.utm_gclid = `${key}-${value}`;
      } else {
        utmParams[key] = value;
      }
    } else {
      if (window.location.pathname === "/" && preQualificationFormKeys.includes(key)) {
        if (!preQualificationFormParams) {
          preQualificationFormParams = {};
        }
        preQualificationFormParams[key] = value;
      } else if (window.location.pathname === "/ft" && fastTrackFormKeys.includes(key)) {
        if (!fastTrackFormParams) {
          fastTrackFormParams = {};
        }
        fastTrackFormParams[key] = value;
      } else {
        otherParams[key] = value;
      }
    }
  }

  return {
    utmParams,
    preQualificationFormParams,
    fastTrackFormParams,
    otherParams,
  };
}
