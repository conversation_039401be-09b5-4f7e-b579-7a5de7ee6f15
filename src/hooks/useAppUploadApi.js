import { useState, useCallback } from "react";
import { API_ENDPOINT } from "../utils/consts";
import { trackFormSubmitted } from "../utils/analytics";

const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Hook for uploading documents to an application
 * @returns {Object} Hook state and methods
 */
export function useAppUploadApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Upload documents to an application
   * @param {string} uuid - Application ID to upload documents to
   * @param {Object} formData - Form data containing bankStatements
   * @returns {Promise<Object>} API response
   */
  const uploadDocs = useCallback(async (uuid, formData) => {
    let result_status = "unknown";
    let error_message = "";

    if (!uuid) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      // Track document upload event
      trackFormSubmitted("Application", {
        event_name: "Uploaded Documents",
        uuid,
        result_status: "success",
      });

      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/${uuid}/upload`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        result_status = "success";

        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      error_message =
        error?.error || error?.message || "There was a problem uploading your documents. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Track failed upload
      trackFormSubmitted("Application", {
        event_name: "Upload Documents Failed",
        uuid,
        result_status: "error",
        error_message,
      });

      // Re-throw the error for the caller to handle
      throw error;
    }
  }, []);

  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    uploadDocs,
    reset,
    status,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    error: error.message,
    errorId: error.id,
    result,
  };
}
