import { useCallback, useState } from "react";
import { useRef } from "react";
import { logger } from "../utils/logger";
import { getRootDomain, cookiesHandler } from "../utils/customCookieStorage";

/**
 * Custom hook to manage cookie storage with a similar interface to useLocalStorage
 * @param {string} key - Cookie name
 * @param {any} initialValue - Default value if cookie doesn't exist
 * @param {Object} options - Cookie options
 * @param {number} options.days - Cookie expiration in days (default: 7)
 * @param {string} options.path - Cookie path (default: '/')
 * @param {string} options.domain - Cookie domain (default: root domain for subdomain sharing)
 * @param {boolean} options.secure - Whether cookie should only be sent over HTTPS (default: true in production)
 * @param {boolean} options.sameSite - SameSite attribute (default: 'Lax')
 * @returns {[any, Function, Function]} - [storedValue, setValue, removeItem]
 */
export function useCookie(key, initialValue, options = {}) {
  const {
    days = 7,
    path = "/",
    domain = getRootDomain(),
    secure = window.location.protocol === "https:",
    sameSite = "Lax",
  } = options;

  const initRef = useRef(false);

  // Initialize state with value from cookie or initialValue
  const [storedValue, setStoredValue] = useState(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const cookieValue = cookiesHandler.get(key);
      return cookieValue !== null ? cookieValue : initialValue;
    } catch (error) {
      logger.error(`Error reading cookie "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value, cookieOptions = {}) => {
      try {
        // Handle function updates
        const valueToStore = value instanceof Function ? value(storedValue) : value;

        if (valueToStore === null || typeof valueToStore === "undefined") {
          cookiesHandler.remove(key, { path, domain });
          setStoredValue(null);
          return;
        } else {
          setStoredValue(valueToStore);
          cookiesHandler.set(key, valueToStore, {
            days,
            path,
            domain,
            secure,
            sameSite,
            ...cookieOptions,
          });
        }
      } catch (error) {
        logger.error(`Error storing cookie "${key}":`, error);
      }
    },
    [storedValue, key, path, domain, days, secure, sameSite],
  );

  // Function to remove the cookie
  const removeItem = useCallback(() => {
    try {
      cookiesHandler.remove(key, { path, domain });
      setStoredValue(initialValue);
    } catch (error) {
      logger.error(`Error removing cookie "${key}":`, error);
    }
  }, [key, initialValue, path, domain]);

  // Initialize cookie with the current value on first render if it doesn't exist
  if (!initRef.current) {
    initRef.current = true;
    if (cookiesHandler.get(key) === null) {
      cookiesHandler.set(key, initialValue, { days, path, domain, secure, sameSite });
    }
  }

  return [storedValue, setValue, removeItem];
}
