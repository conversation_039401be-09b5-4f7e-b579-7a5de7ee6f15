import { useState, useCallback } from "react";
import { trackFormSubmitted, trackCustomEvent } from "../utils/analytics";
import { API_ENDPOINT } from "../utils/consts";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the appComplete API call
 * This API is called when a user completes the final step of the application
 * @returns {Object} API state and methods
 */
export function useAppCompleteApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Complete an application
   * @param {string} uuid - Application ID to complete
   * @param {Object} formData - Form data to submit (bankStatements only)
   * @returns {Promise<Object>} API response
   */
  const completeApp = useCallback(async (uuid, formData) => {
    let result_status = "unknown";
    let error_message = "";

    if (!uuid) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      // Track bank statements upload event
      trackFormSubmitted("Application", {
        event_name: "Uploaded Bank Statements",
        uuid,
        result_status: "success", // Assuming upload is successful at this point
      });

      // Call the API directly using fetch
      const response = await fetch(`${API_ENDPOINT}/app/${uuid}/complete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        result_status = "success";

        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      result_status = "error";
      error_message =
        error?.error || error?.message || "There was a problem completing your application. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    } finally {
      trackFormSubmitted("Application", {
        event_name: "Completed Application",
        uuid,
        result_status,
        ...(result_status === "error" && { error_message }),
      });
      trackCustomEvent("application_completed", true);
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    completeApp,
    reset,
  };
}
