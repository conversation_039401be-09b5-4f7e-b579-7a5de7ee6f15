import { useCallback, useMemo } from "react";
import { useCookie } from "./useCookie";
import {
  PRE_QUALIFY_FORM_KEY,
  PRE_QUALIFY_RESULT_KEY,
  FAST_TRACK_FORM_KEY,
  FAST_TRACK_ACTIVE_KEY,
  APPLICATION_FORM_KEY,
  APPLICATION_RESULT_KEY,
  APPLICATION_ID_KEY,
  APPLICATION_STARTED_KEY,
  defaultPreQualifyValues,
  defaultFastTrackValues,
  defaultApplicationValues,
} from "../utils/consts";
import { utmCookiesHandler } from "../utils/customCookieStorage";

/**
 * Hook to manage all application storage in cookies
 * @returns {Object} Methods to get, set, and clear application data
 */
export function useAppStorage() {
  // Pre-qualification form data (stored in cookies with 7-day expiration)
  const [preQualifyForm, setPreQualifyForm] = useCookie(PRE_QUALIFY_FORM_KEY, defaultPreQualifyValues);

  // Pre-qualification result data (stored in cookies with 7-day expiration)
  const [preQualifyResult, setPreQualifyResult] = useCookie(PRE_QUALIFY_RESULT_KEY, null);

  // Fast track form data (stored in cookies with 7-day expiration)
  const [fastTrackForm, setFastTrackForm] = useCookie(FAST_TRACK_FORM_KEY, defaultFastTrackValues);

  // Fast track active status (stored in cookies with default 7-day expiration)
  const [fastTrackActive, setFastTrackActive] = useCookie(FAST_TRACK_ACTIVE_KEY, false);

  // Application form data (stored in cookies with 7-day expiration)
  const [applicationForm, setApplicationForm] = useCookie(APPLICATION_FORM_KEY, defaultApplicationValues);

  // Application result data (stored in cookies with 7-day expiration)
  const [applicationResult, setApplicationResult] = useCookie(APPLICATION_RESULT_KEY, null);

  // Application ID (stored in cookies with 7-day expiration)
  const [applicationId, setApplicationId] = useCookie(APPLICATION_ID_KEY, "");

  // Application started status (stored in cookies with 7-day expiration)
  const [applicationStarted, setApplicationStarted] = useCookie(APPLICATION_STARTED_KEY, false);

  const utmParams = useMemo(() => {
    return utmCookiesHandler.collect();
  }, []);

  const setUtmParams = useCallback((newUtmParams) => {
    if (newUtmParams && typeof newUtmParams === "object") {
      const currentUtmParams = utmCookiesHandler.collect();

      Object.entries(newUtmParams).forEach(([key, value]) => {
        if (key.startsWith("utm_") && value) {
          // Only set the cookie if the value has actually changed
          if (currentUtmParams[key] !== value) {
            utmCookiesHandler.set(key, value);
          }
        }
      });
    }
  }, []);

  /**
   * Clear pre-qualification form data
   */
  const clearPreQualifyForm = useCallback(() => {
    setPreQualifyForm(defaultPreQualifyValues);
  }, [setPreQualifyForm]);

  /**
   * Clear pre-qualification result data from cookies
   */
  const clearPreQualifyResult = useCallback(() => {
    setPreQualifyResult(null);
  }, [setPreQualifyResult]);

  /**
   * Clear fast track form data
   */
  const clearFastTrackForm = useCallback(() => {
    setFastTrackForm(defaultFastTrackValues);
  }, [setFastTrackForm]);

  /**
   * Clear fast track active status
   */
  const clearFastTrackActive = useCallback(() => {
    setFastTrackActive(false);
  }, [setFastTrackActive]);

  /**
   * Clear application form data
   */
  const clearApplicationForm = useCallback(() => {
    setApplicationForm(defaultApplicationValues);
  }, [setApplicationForm]);

  /**
   * Clear application result data from cookies
   */
  const clearApplicationResult = useCallback(() => {
    setApplicationResult(null);
  }, [setApplicationResult]);

  /**
   * Clear application ID
   */
  const clearApplicationId = useCallback(() => {
    setApplicationId("");
  }, [setApplicationId]);

  /**
   * Clear application started status
   */
  const clearApplicationStarted = useCallback(() => {
    setApplicationStarted(false);
  }, [setApplicationStarted]);

  /**
   * Clear all pre-qualification data (form and result)
   */
  const clearAllPreQualifyData = useCallback(() => {
    clearPreQualifyForm();
    clearPreQualifyResult();
    clearApplicationId();
  }, [clearPreQualifyForm, clearPreQualifyResult, clearApplicationId]);

  /**
   * Clear all application data (form and result)
   */
  const clearAllApplicationData = useCallback(() => {
    clearApplicationForm();
    clearApplicationResult();
  }, [clearApplicationForm, clearApplicationResult]);

  /**
   * Clear all data except UTM parameters
   */
  const clearAllData = useCallback(() => {
    setApplicationId("");
    clearAllPreQualifyData();
    clearAllApplicationData();
    clearFastTrackForm();
    clearFastTrackActive();
    clearApplicationStarted();
  }, [
    clearAllPreQualifyData,
    clearFastTrackForm,
    clearFastTrackActive,
    clearAllApplicationData,
    setApplicationId,
    clearApplicationStarted,
  ]);

  /**
   * Update pre-qualification form data
   * @param {Object|Function} value - New value or function to update value
   */
  const updatePreQualifyForm = useCallback(
    (value) => {
      if (typeof value === "function") {
        setPreQualifyForm((prev) => ({
          ...prev,
          ...value(prev),
        }));
      } else {
        setPreQualifyForm((prev) => ({
          ...prev,
          ...value,
        }));
      }
    },
    [setPreQualifyForm],
  );

  /**
   * Update fast track form data
   * @param {Object|Function} value - New value or function to update value
   */
  const updateFastTrackForm = useCallback(
    (value) => {
      if (typeof value === "function") {
        setFastTrackForm((prev) => ({
          ...prev,
          ...value(prev),
        }));
      } else {
        setFastTrackForm((prev) => ({
          ...prev,
          ...value,
        }));
      }
    },
    [setFastTrackForm],
  );

  /**
   * Update application form data
   * @param {Object|Function} value - New value or function to update value
   */
  const updateApplicationForm = useCallback(
    (value) => {
      if (typeof value === "function") {
        setApplicationForm((prev) => ({
          ...prev,
          ...value(prev),
        }));
      } else {
        setApplicationForm((prev) => ({
          ...prev,
          ...value,
        }));
      }
    },
    [setApplicationForm],
  );

  return {
    // Data getters
    preQualifyForm,
    preQualifyResult,
    fastTrackForm,
    fastTrackActive,
    applicationForm,
    applicationResult,
    applicationId,
    applicationStarted,
    utmParams,

    // Data setters
    setPreQualifyForm,
    setPreQualifyResult,
    setFastTrackForm: updateFastTrackForm,
    setFastTrackActive,
    setApplicationForm: updateApplicationForm,
    setApplicationResult,
    setApplicationId,
    setApplicationStarted,
    setUtmParams,

    // Partial update methods
    updatePreQualifyForm,
    updateFastTrackForm,
    updateApplicationForm,

    // Clear methods
    clearPreQualifyForm,
    clearPreQualifyResult,
    clearFastTrackForm,
    clearFastTrackActive,
    clearApplicationForm,
    clearApplicationResult,
    clearApplicationId,
    clearApplicationStarted,
    clearAllPreQualifyData,
    clearAllApplicationData,
    clearAllData,
  };
}
