import { logger } from "./logger";

export const GTM_ID = import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID;

/**
 * Initialize Google Tag Manager
 */
export function initializeGTM() {
  if (typeof window === "undefined") return;

  if (!GTM_ID) {
    logger.log("Google Tag Manager ID is not provided", "GTM");
    return;
  }

  // Create script element for Google Tag Manager
  try {
    const inlineScript = document.createElement("script");
    inlineScript.innerHTML = `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${GTM_ID}');`;

    // Insert at the beginning of the head
    const head = document.getElementsByTagName("head")[0];
    head.insertBefore(inlineScript, head.firstChild);

    // noscript element
    const noscript = document.createElement("noscript");
    const iframe = document.createElement("iframe");

    iframe.src = `https://www.googletagmanager.com/ns.html?id=${GTM_ID}`;
    iframe.height = "0";
    iframe.width = "0";
    iframe.style.display = "none";
    iframe.style.visibility = "hidden";

    noscript.appendChild(iframe);

    // Insert immediately after the opening body tag
    const body = document.body;
    body.insertBefore(noscript, body.firstChild);

    logger.log(`Google Tag Manager initialized with ID: ${GTM_ID}`, "GTM");
  } catch (error) {
    logger.error("Failed to initialize Google Tag Manager:", error, "GTM");
  }
}
