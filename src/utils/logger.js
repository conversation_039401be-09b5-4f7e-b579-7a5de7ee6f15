import { IS_DEV_MODE } from "./consts";

// Check if debug mode is enabled via URL parameter
const hasDebugParam = () => {
  if (import.meta.env.VITE_DEBUG_MODE === "true") {
    console.info("[DEBUG] enabled via env");
    return true;
  }
  if (typeof window === "undefined") return false;
  const urlParams = new URLSearchParams(window.location.search);
  const debugParam = urlParams.get("debug");
  if (debugParam === "true") return true;
  return false;
};

const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleInfo = console.info;
const originalConsoleDebug = console.debug;

const isDebugMode = IS_DEV_MODE || hasDebugParam();

export const logger = {
  log: (...args) => {
    if (isDebugMode) {
      originalConsoleLog(...args);
    }
  },
  error: (...args) => {
    if (isDebugMode) {
      originalConsoleError(...args);
    }
  },
  warn: (...args) => {
    if (isDebugMode) {
      originalConsoleWarn(...args);
    }
  },
  info: (...args) => {
    if (isDebugMode) {
      originalConsoleInfo(...args);
    }
  },
  debug: (...args) => {
    if (isDebugMode) {
      originalConsoleDebug(...args);
    }
  },
};

console.log = logger.log;
console.error = logger.error;
console.warn = logger.warn;
console.info = logger.info;
console.debug = logger.debug;
