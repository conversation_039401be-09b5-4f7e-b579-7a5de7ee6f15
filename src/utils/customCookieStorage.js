import { logger } from "../utils/logger";

class Cookies {
  get(name) {
    if (typeof window === "undefined") return null;

    const cookies = document.cookie.split("; ");
    const cookie = cookies.find((c) => c.startsWith(`${name}=`));

    if (!cookie) return null;

    const cookieValue = cookie.split("=")[1];

    try {
      return JSON.parse(decodeURIComponent(cookieValue));
    } catch {
      return decodeURIComponent(cookieValue);
    }
  }

  set(name, value, options = {}) {
    const {
      days = 7,
      path = "/",
      domain = getRootDomain(),
      secure = window.location.protocol === "https:",
      sameSite = "Lax",
    } = options;

    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);

    let cookieValue;

    if (typeof value === "string") {
      cookieValue = encodeURIComponent(value);
    } else {
      cookieValue = encodeURIComponent(JSON.stringify(value));
    }

    document.cookie = `${name}=${cookieValue}; expires=${expires.toUTCString()}; path=${path}${
      domain ? `; Domain=${domain}` : ""
    }${secure ? "; Secure" : ""}; SameSite=${sameSite}`;
  }

  remove(name, options = {}) {
    const { path = "/", domain = getRootDomain() } = options;
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${
      domain ? `; Domain=${domain}` : ""
    }`;
  }
}

export const cookiesHandler = new Cookies();

class UtmCookies {
  #STORAGE_KEY = "utmParams";
  #STORAGE_TIMESTAMP_KEY = `${this.#STORAGE_KEY}_ts`;

  constructor() {
    // this will remove expired utmParams from local storage
    this.#getUtmLocalStorageAll();
  }

  get(utmKey) {
    if (!utmKey.startsWith("utm_")) {
      throw new Error('UTM key must start with "utm_"');
    }

    try {
      const cookieValue = cookiesHandler.get(utmKey);
      if (cookieValue !== null) {
        return cookieValue;
      }
    } catch (error) {
      logger.warn(`Failed to get UTM cookie ${utmKey}:`, error);
    }

    // Fallback to local storage
    return this.#getUtmLocalStorage(utmKey);
  }

  set(utmKey, value, options = {}) {
    if (!utmKey.startsWith("utm_")) {
      throw new Error('UTM key must start with "utm_"');
    }

    try {
      cookiesHandler.set(utmKey, value, options);
    } catch (error) {
      logger.warn(`Failed to set UTM cookie ${utmKey}:`, error);
    } finally {
      // Always store in local storage as fallback
      this.#setUtmLocalStorage(utmKey, value);
    }
  }

  remove(utmKey, options = {}) {
    if (!utmKey.startsWith("utm_")) {
      throw new Error('UTM key must start with "utm_"');
    }
    cookiesHandler.remove(utmKey, options);
  }

  collect() {
    let utmParams = {};
    try {
      utmParams = this.#collectUtmCookiesHelper();
    } catch (error) {
      logger.warn("Failed to collect UTM cookies:", error);
    } finally {
      if (Object.keys(utmParams).length === 0) {
        logger.log("No UTM cookies found, falling back to local storage");
        utmParams = this.#getUtmLocalStorageAll();
      }
    }

    return utmParams;
  }

  clear() {
    const utmParams = this.collect();
    Object.keys(utmParams).forEach((key) => {
      this.remove(key);
    });
    localStorage.removeItem(this.#STORAGE_KEY);
  }

  #collectUtmCookiesHelper() {
    if (typeof window === "undefined") return {};

    const utmParams = {};
    const cookies = document.cookie.split("; ");

    cookies.forEach((cookie) => {
      const [name, value] = cookie.split("=");
      if (name && name.startsWith("utm_") && value) {
        try {
          utmParams[name] = String(JSON.parse(decodeURIComponent(value)));
        } catch {
          utmParams[name] = decodeURIComponent(value);
        }
      }
    });

    return utmParams;
  }

  #setUtmLocalStorage(utmKey, value) {
    try {
      const existingUtmParams = this.#getUtmLocalStorageAll();
      existingUtmParams[utmKey] = value;
      localStorage.setItem(this.#STORAGE_KEY, JSON.stringify(existingUtmParams));
      localStorage.setItem(this.#STORAGE_TIMESTAMP_KEY, Date.now().toString());
    } catch (error) {
      logger.warn(`Failed to set UTM local storage ${utmKey}:`, error);
    }
  }

  #getUtmLocalStorage(utmKey) {
    try {
      const utmParams = this.#getUtmLocalStorageAll();
      return utmParams[utmKey] || null;
    } catch (error) {
      logger.warn(`Failed to get UTM local storage ${utmKey}:`, error);
      return null;
    }
  }

  #getUtmLocalStorageAll() {
    try {
      const ts = Number(localStorage.getItem(this.#STORAGE_TIMESTAMP_KEY));
      if (!ts || Date.now() - ts > 7 * 24 * 60 * 60 * 1000) {
        localStorage.removeItem(this.#STORAGE_KEY);
        localStorage.removeItem(this.#STORAGE_TIMESTAMP_KEY);
        return {};
      }
      const stored = localStorage.getItem(this.#STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      logger.warn("Failed to get all UTM local storage:", error);
      return {};
    }
  }
}

export const utmCookiesHandler = new UtmCookies();

export function getRootDomain() {
  const hostname = window.location.hostname;

  // For localhost or IP addresses, don't set domain
  if (hostname === "localhost" || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null;
  }

  // Split hostname and get the last two parts (domain.tld)
  const parts = hostname.split(".");
  if (parts.length >= 2) {
    return `.${parts.slice(-2).join(".")}`;
  }

  return null;
}
