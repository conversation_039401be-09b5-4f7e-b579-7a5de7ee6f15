// Utility to load Trustpilot script only once
let trustpilotScriptPromise;

export function loadTrustpilotScript() {
  if (trustpilotScriptPromise) return trustpilotScriptPromise;
  trustpilotScriptPromise = new Promise((resolve, reject) => {
    if (window.Trustpilot) {
      resolve();
      return;
    }
    const existingScript = document.querySelector('script[src*="tp.widget.bootstrap.min.js"]');
    if (existingScript) {
      existingScript.addEventListener("load", () => resolve());
      existingScript.addEventListener("error", reject);
      // If already loaded
      if (window.Trustpilot) resolve();
      return;
    }
    const script = document.createElement("script");
    script.src = "https://widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js";
    script.async = true;
    script.onload = () => resolve();
    script.onerror = reject;
    document.body.appendChild(script);
  });
  return trustpilotScriptPromise;
}
