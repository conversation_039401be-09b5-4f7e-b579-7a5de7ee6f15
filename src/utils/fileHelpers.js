/**
 * Converts a File object to a base64 string
 * @param {File} file - The file to convert
 * @returns {Promise<string>} - A promise that resolves to the base64 string
 */
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

/**
 * Converts a base64 string to a File object
 * @param {Object} fileData - Object containing file data
 * @param {string} fileData.dataUrl - The base64 data URL
 * @param {string} fileData.name - The file name
 * @param {string} fileData.type - The file MIME type
 * @returns {File} - A new File object
 */
export const base64ToFile = (fileData) => {
  // Extract the base64 data from the data URL
  const arr = fileData.dataUrl.split(",");
  // const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  // Create a new File object
  return new File([u8arr], fileData.name, { type: fileData.type });
};

/**
 * Creates a preview URL for a file
 * @param {File} file - The file to create a preview for
 * @returns {string} - The preview URL
 */
export const createFilePreview = (file) => {
  return URL.createObjectURL(file);
};

/**
 * Checks if a file object is a serialized file (has dataUrl)
 * @param {Object} file - The file object to check
 * @returns {boolean} - True if the file is serialized
 */
export const isSerializedFile = (file) => {
  return file && typeof file === "object" && "dataUrl" in file && typeof file.dataUrl === "string";
};

/**
 * Checks if a file object is a File instance
 * @param {Object} file - The file object to check
 * @returns {boolean} - True if the file is a File instance
 */
export const isFileInstance = (file) => {
  return file instanceof File;
};
