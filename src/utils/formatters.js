export const formatCurrency = (value) => {
  // Handle empty or undefined values
  if (value === undefined || value === null || value === "") {
    return "";
  }

  // Remove non-digit characters
  const digits = value.toString().replace(/\D/g, "");

  // Handle zero or empty string after removing non-digits
  if (!digits || digits === "0") {
    return "";
  }

  // Convert to number and format with commas
  const number = parseInt(digits, 10);
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(number);
};

export const parseCurrency = (formattedValue) => {
  // Handle empty values
  if (!formattedValue) {
    return "";
  }

  // Extract only digits from the formatted string
  const digits = formattedValue.toString().replace(/\D/g, "");
  return digits ? parseInt(digits, 10) : "";
};

export const formatPhoneNumber = (value) => {
  // Handle empty or undefined values
  if (!value) {
    return "";
  }

  // Remove non-digit characters
  const digits = value.toString().replace(/\D/g, "");

  // Format based on the number of digits
  if (digits.length === 0) {
    return "";
  } else if (digits.length <= 3) {
    return `(${digits}`;
  } else if (digits.length <= 6) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
  } else {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  }
};

export const parsePhoneNumber = (formattedValue) => {
  // Handle empty values
  if (!formattedValue) {
    return "";
  }

  // Extract only digits from the formatted string
  return formattedValue.toString().replace(/\D/g, "");
};

export const formatEIN = (value) => {
  // Handle empty or undefined values
  if (!value) {
    return "";
  }

  // Remove non-digit characters
  const digits = value.toString().replace(/\D/g, "");

  // Format based on the number of digits (XX-XXXXXXX)
  if (digits.length === 0) {
    return "";
  } else if (digits.length <= 2) {
    return digits;
  } else {
    return `${digits.slice(0, 2)}-${digits.slice(2, 9)}`;
  }
};

export const maskEIN = (value) => {
  // Handle empty or undefined values
  if (!value) {
    return "";
  }

  // Remove non-digit characters
  const digits = value.toString().replace(/\D/g, "");

  // Only mask if we have a complete EIN (9 digits)
  if (digits.length === 9) {
    return `XX-XXX${digits.slice(5, 9)}`;
  }

  // Otherwise return the regular formatted value
  return formatEIN(value);
};

export const parseEIN = (formattedValue) => {
  // Handle empty values
  if (!formattedValue) {
    return "";
  }

  // Extract only digits from the formatted string
  return formattedValue.toString().replace(/\D/g, "");
};

export const formatSSN = (value) => {
  // Handle empty or undefined values
  if (!value) {
    return "";
  }

  // Remove non-digit characters
  const digits = value.toString().replace(/\D/g, "");

  // Format based on the number of digits (XXX-XX-XXXX)
  if (digits.length === 0) {
    return "";
  } else if (digits.length <= 3) {
    return digits;
  } else if (digits.length <= 5) {
    return `${digits.slice(0, 3)}-${digits.slice(3)}`;
  } else {
    return `${digits.slice(0, 3)}-${digits.slice(3, 5)}-${digits.slice(5, 9)}`;
  }
};

export const maskSSN = (value) => {
  // Handle empty or undefined values
  if (!value) {
    return "";
  }

  // Remove non-digit characters
  const digits = value.toString().replace(/\D/g, "");

  // Only mask if we have a complete SSN (9 digits)
  if (digits.length === 9) {
    return `XXX-XX-${digits.slice(5, 9)}`;
  }

  // Otherwise return the regular formatted value
  return formatSSN(value);
};

export const parseSSN = (formattedValue) => {
  // Handle empty values
  if (!formattedValue) {
    return "";
  }

  // Extract only digits from the formatted string
  return formattedValue.toString().replace(/\D/g, "");
};
