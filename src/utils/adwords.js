import { logger } from "./logger";
import { cookies<PERSON><PERSON><PERSON>, utmCookiesHand<PERSON> } from "./customCookieStorage";

export const AdwordsConversions = {
  PREQUAL_APPROVED: "AW-16778466762/Bv7HCMSRkO0aEMqrzMA-",
  PREQUAL_DENIED: "AW-16778466762/qqEiCIn0ne0aEMqrzMA-",
};

const log = (...args) => {
  logger.log("[AdWords]: ", ...args);
};

export function trackConversion(key) {
  const utmParams = utmCookiesHandler.collect();
  if (utmParams?.utm_source?.toLowerCase() !== "googleadwords") return;

  const conversionId = AdwordsConversions[key];
  if (!conversionId) {
    log(`Unknown Adwords conversion key: ${key}`);
    return;
  }

  // use shared key for prequal conversions
  const isPrequal = key === "PREQUAL_APPROVED" || key === "PREQUAL_DENIED";
  const cookieKey = isPrequal ? "aw_tracked_PREQUAL_RESULT" : `aw_tracked_${key}`;

  if (cookiesHandler.get(cookieKey)) {
    log("Skipping already tracked adwords conversion:", key);
    return;
  }

  if (!window.gtag) return;

  cookiesHandler.set(cookieKey, "true", { days: 1 });

  log("Tracking Adwords Conversion:", conversionId);
  window.gtag("event", "conversion", { send_to: conversionId });
}
