import { logger } from "./logger";

export const HOTJAR_ID = import.meta.env.VITE_HOTJAR_ID;

export function initializeHotjar() {
  if (typeof window === "undefined") return;

  if (!HOTJAR_ID) {
    logger.log("Hotjar ID is not provided", "Hotjar");
    return;
  }

  // Create script element for Hotjar
  try {
    const inlineScript = document.createElement("script");
    inlineScript.innerHTML = `(function(h,o,t,j,a,r){
    h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
    h._hjSettings={hjid:${HOTJAR_ID},hjsv:6};
    a=o.getElementsByTagName('head')[0];
    r=o.createElement('script');r.async=1;
    r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
    a.appendChild(r);
})(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`;
    document.head.appendChild(inlineScript);

    logger.log(`Hotjar initialized with ID: ${HOTJAR_ID}`, "Hotjar");
  } catch (error) {
    logger.error("Failed to initialize Hotjar:", error, "Hotjar");
  }
}
