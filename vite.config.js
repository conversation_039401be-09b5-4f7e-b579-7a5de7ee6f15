import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import UnpluginInjectPreload from "unplugin-inject-preload/vite";

export default defineConfig(({ mode }) => {
  // eslint-disable-next-line no-undef
  const env = loadEnv(mode, process.cwd(), "");

  return {
    define: {
      "import.meta.env.CF_PAGES": env.CF_PAGES,
    },

    build: {
      rollupOptions: {
        output: {
          entryFileNames: `assets/[hash].js`,
          chunkFileNames: `assets/[hash].js`,
          assetFileNames: `assets/[hash][extname]`,
          manualChunks: (id) => {
            // React core libraries
            if (id.includes("react") || id.includes("react-dom") || id.includes("react-router")) {
              return "react-vendor";
            }

            if (id.includes("node_modules")) {
              return "vendor";
            }

            return undefined;
          },
        },
      },
    },

    plugins: [
      react(),
      tailwindcss(),
      UnpluginInjectPreload({
        files: [
          {
            entryMatch: /lexend-latin-wght-normal.*\.woff2$/,
            attributes: {
              rel: "preload",
              as: "font",
              type: "font/woff2",
              crossorigin: "",
            },
          },
          {
            entryMatch: /index.*\.js$/,
            attributes: {
              rel: "modulepreload",
            },
          },
          {
            entryMatch: /react-vendor.*\.js$/,
            attributes: {
              rel: "modulepreload",
            },
          },
          {
            entryMatch: /.*Page.*|.*Result.*/,
            attributes: {
              rel: "modulepreload",
            },
          },
          {
            // Preload general vendor chunk
            entryMatch: /vendor.*\.js$/,
            attributes: {
              rel: "modulepreload",
            },
          },
        ],
      }),
    ],
  };
});
