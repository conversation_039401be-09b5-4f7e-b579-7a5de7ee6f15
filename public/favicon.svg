<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="1000" height="1000"><g clip-path="url(#SvgjsClipPath1094)"><rect width="1000" height="1000" fill="#ffffff"></rect><g transform="matrix(1.3671875,0,0,1.3671875,150,150)"><svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="512" height="512" viewBox="0 0 512 512"><image width="512" height="512" xlink:href="data:image/png;base64,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"></image></svg></g></g><defs><clipPath id="SvgjsClipPath1094"><rect width="1000" height="1000" x="0" y="0" rx="500" ry="500"></rect></clipPath></defs></svg>